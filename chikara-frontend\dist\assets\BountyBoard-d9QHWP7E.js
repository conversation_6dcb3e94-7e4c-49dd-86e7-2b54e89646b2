import{b as h,c as i,e as f,g as N,ap as s,j as e,r as c,C as k,t as j,F as v,G as w,y as B}from"./index-BNv9-hhu.js";import{C}from"./Callout-BPProctn.js";import{U as I}from"./UsersTable-B_rLgqO9.js";const S=r=>h(i.bounties.getActiveBountyList.queryOptions({staleTime:3e4,enabled:r?.enabled!==!1})),E=r=>{const n=f();return N(i.bounties.placeBounty.mutationOptions({onSuccess:()=>{n.invalidateQueries({queryKey:i.bounties.getActiveBountyList.key()}),n.invalidateQueries({queryKey:i.user.getCurrentUserInfo.key()}),s.success("Bounty placed successfully!"),r&&r()},onError:a=>{const o=a.message||"Failed to place bounty";console.error(o),s.error(o)}}))};function A(){const{data:r,isLoading:n,error:a}=S();return a?e.jsxs("div",{children:["An error has occurred: ",a.message]}):e.jsxs("div",{className:"mb-8 pb-4 md:mx-auto md:mb-0 md:max-w-6xl md:pb-0",children:[e.jsx(C,{className:"mt-3 mb-4",title:"Here you will find students that have been placed on the bounty list.",children:e.jsx("p",{className:"text-gray-300!",children:"You must defeat the student in a PvP battle and choose to 'Cripple' them in order to claim the bounty reward!"})}),e.jsx(F,{}),e.jsx(I,{data:r,isLoading:n,type:"bounties"})]})}const F=()=>{const[r,n]=c.useState(""),[a,o]=c.useState(""),[l,m]=c.useState(""),{MIN_BOUNTY:d,BOUNTY_FEE:x}=k(),{data:y}=j(),g=E(()=>{n(""),o(""),m("")}),b=()=>{if(!r.trim()){s.error("Enter a valid Student ID");return}if(!a.trim()){s.error("Enter a reward amount");return}if(!l.trim()){s.error("Enter a bounty reason");return}const t=Number.parseInt(a),u=Number.parseInt(r);if(isNaN(t)||t<d){s.error(`Bounty rewards must be at least ${B(d)}`);return}if(isNaN(u)||u<=0){s.error("Enter a valid Student ID");return}const p=x+1;if(t*p>(y?.cash||0)){s.error("You need more cash to place this bounty!");return}g.mutate({amount:t,targetId:u,reason:l})};return e.jsxs("div",{className:"mb-4 grid h-36 grid-cols-6 grid-rows-2 gap-x-4 gap-y-2 px-4 md:flex md:h-16 md:gap-5 md:px-0",children:[e.jsxs("div",{className:"col-span-3 md:w-[10%]",children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-xs uppercase tracking-wide dark:text-gray-200",htmlFor:"studentid",children:["Student ID",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsxs("div",{className:"mt-1 flex rounded-md shadow-xs",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-300 border-r-0 bg-gray-50 px-3 text-gray-500 sm:text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",children:"#"}),e.jsx("input",{type:"number",name:"studentid",min:1,id:"studentid",value:r,className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"1",onChange:t=>{n(t.target.value)}})]})]}),e.jsxs("div",{className:"col-span-3 md:w-1/6",children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-xs uppercase tracking-wide dark:text-gray-200",htmlFor:"amount",children:["Reward",e.jsx("span",{className:"text-red-500",children:" *"})," ",e.jsxs("span",{className:"text-gray-400",children:["(",x*100,"% fee)"]})]}),e.jsxs("div",{className:"mt-1 flex rounded-md shadow-xs",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-300 border-r-0 bg-gray-50 px-3 text-gray-500 sm:text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",children:v("yen")}),e.jsx("input",{type:"number",name:"amount",id:"amount",value:a,min:d,className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:d.toString(),onChange:t=>{o(t.target.value)}})]})]}),e.jsxs("div",{className:"col-span-4 md:w-2/6",children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-xs uppercase tracking-wide dark:text-gray-200",htmlFor:"reason",children:["Reason",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:e.jsx("input",{type:"text",name:"reason",id:"reason",value:l,className:"block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"No Reason",onChange:t=>{m(t.target.value)}})})]}),e.jsx(w,{isLoading:g.isPending,variant:"flat",className:"col-span-2 mt-auto",textSize:"text-xs md:text-sm",onClick:()=>b(),children:"Place Bounty"})]})};export{A as default};
