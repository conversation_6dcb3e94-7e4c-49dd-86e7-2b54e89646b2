import { useEffect, useState } from "react";
import EmptyTalentSpace from "./EmptyTalentSpace";
import LockedTalent from "./LockedTalent";
import MainTalent from "./MainTalent";
import type { TalentInfo, UserTalent } from "@/features/talents/types/talents";

interface DisplayTalentProps {
    talent?: TalentInfo | null;
    selectedTalent?: TalentInfo | null;
    setSelectedTalent: (talent?: TalentInfo | null) => void;
    isTalentDisabled?: boolean;
    showEmptySpaces?: boolean;
    unlockedTalents?: UserTalent[];
}

export default function DisplayTalent({
    talent,
    selectedTalent,
    setSelectedTalent,
    isTalentDisabled,
    showEmptySpaces,
    unlockedTalents,
}: DisplayTalentProps) {
    const [unlockedTalent, setUnlockedTalent] = useState<UserTalent | null>(null);

    const talentUnlocked = () => {
        const unlockedTalent = unlockedTalents?.find((t) => t?.talentId === talent?.id);
        return unlockedTalent || null;
    };

    useEffect(() => {
        setUnlockedTalent(talentUnlocked());
    }, [unlockedTalents, talent?.id]);

    if (talent?.talentType === "locked")
        return <LockedTalent talent={talent} selectedTalent={selectedTalent} setSelectedTalent={setSelectedTalent} />;

    if (talent?.talentType === "empty") {
        if (showEmptySpaces) {
            return <EmptyTalentSpace />;
        } else {
            return (
                <LockedTalent
                    talent={talent}
                    selectedTalent={selectedTalent}
                    setSelectedTalent={setSelectedTalent}
                    isTalentDisabled={isTalentDisabled}
                />
            );
        }
    }

    if (talent?.talentType === "passive" || talent?.talentType === "ability")
        return (
            <MainTalent
                talent={talent}
                selectedTalent={selectedTalent}
                setSelectedTalent={setSelectedTalent}
                isTalentDisabled={isTalentDisabled}
                talentUnlocked={unlockedTalent}
            />
        );

    return null;
}
