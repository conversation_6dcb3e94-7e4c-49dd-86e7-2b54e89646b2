import{bL as r,j as e,h as c}from"./index-BNv9-hhu.js";import{p as d}from"./patchNotes-CWZ9Xpkl.js";import{V as n}from"./disclosure-6cPc6BxT.js";import{C as x}from"./chevron-up-pgv_lXQk.js";const m=({note:s})=>e.jsxs("div",{className:"my-2 ml-4 list-disc",children:[e.jsx("h3",{className:"font-semibold text-custom-yellow text-lg",children:s.heading}),e.jsx("h2",{className:"font-semibold text-base text-blue-500 leading-none",children:s.h2Heading}),s.subheading&&e.jsxs("p",{className:"font-semibold text-black text-sm dark:text-red-400",children:[s.subheading," ",s.subheadinglink&&e.jsx("a",{target:"_blank",rel:"noreferrer",className:"text-blue-500 underline",href:s.subheadinglink,children:s.subheadinglink})]}),e.jsxs("div",{className:"ml-6",children:[s.content?.map((t,a)=>e.jsx("li",{className:"text-black text-sm dark:text-slate-200",children:t},a)),s.content2?.map((t,a)=>e.jsx("li",{className:"text-black text-sm dark:text-slate-200",children:t},a))]}),s.footer&&e.jsx("p",{className:"mt-2 font-medium text-sm text-white",children:s.footer})]}),o=({update:s})=>e.jsxs(n.Panel,{className:"rounded-b-lg border-x border-b p-4 text-gray-500 text-sm shadow-xl dark:border-slate-600 dark:bg-slate-900 dark:text-slate-300",children:[e.jsx("p",{className:"font-semibold text-black text-lg dark:text-white",children:s.heading}),s.subheading&&e.jsx("p",{className:"my-2 text-black dark:text-slate-300",children:s.subheading}),e.jsx("ul",{children:s.patchNotes?.map((t,a)=>e.jsx(m,{note:t},a))}),s.content1Description&&e.jsx("p",{className:"mt-2 text-gray-300",children:s.content1Description}),s.subheading2&&e.jsxs("p",{className:"my-2 text-black dark:text-slate-300",children:[e.jsx("br",{}),s.subheading2]}),s.content2&&e.jsx("ul",{children:s.content2.map((t,a)=>e.jsx("li",{className:"ml-5 list-disc",children:t},a))}),s.content2Description&&e.jsx("p",{className:"mt-2 text-gray-300",children:s.content2Description})]}),h=({update:s,isOpen:t,i:a})=>e.jsx(n,{as:"div",className:"my-2",defaultOpen:t,children:({open:l})=>e.jsxs(e.Fragment,{children:[e.jsxs(n.Button,{className:c(l?"rounded-t-lg":"rounded-lg",a===0?"bg-blue-600":"bg-indigo-800","focus-visible:ring/10 flex w-full justify-between px-4 py-3 text-left font-medium text-lg focus:outline-hidden dark:text-slate-200 dark:text-stroke-sm"),children:[e.jsxs("div",{className:"flex gap-4",children:[e.jsx("span",{className:"w-28 font-semibold",children:s.date}),e.jsx("span",{className:"font-semibold",children:s.version}),e.jsx("span",{className:"font-semibold",children:s?.summary})]}),e.jsx(x,{className:`${l?"rotate-180":""} my-auto size-6 text-white`})]}),e.jsx(o,{update:s})]})});function N(){const[s]=r(),t=s.get("id"),a=[...d].reverse();return e.jsx("div",{className:"w-full px-4 text-shadow",children:e.jsx("div",{className:"mx-auto my-2 w-full max-w-3xl rounded-2xl bg-white p-2 font-body dark:bg-slate-800",children:a.map((l,i)=>e.jsx(h,{update:l,isOpen:l.id===parseInt(t||"0"),i},l.id))})})}export{N as default};
