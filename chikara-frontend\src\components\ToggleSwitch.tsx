import { cn } from "@/lib/utils";
import * as Label from "@radix-ui/react-label";
import * as Switch from "@radix-ui/react-switch";

interface ToggleSwitchProps {
    label: string;
    description?: string;
    value: boolean;
    onChange: (_value: boolean) => void;
    disabled?: boolean;
    className?: string;
}

const ToggleSwitch = ({
    label,
    description,
    value,
    onChange,
    disabled = false,
    className = "pt-4",
}: ToggleSwitchProps) => {
    return (
        <div className={cn("flex items-center justify-between", className)}>
            <div className="flex flex-col">
                <Label.Root className="font-medium text-indigo-500 text-sm dark:text-stroke-sm">{label}</Label.Root>
                {description && <p className="pb-4 text-gray-500 text-sm dark:text-gray-300">{description}</p>}
            </div>

            <Switch.Root
                disabled={disabled}
                checked={value}
                className={cn(
                    value ? "bg-indigo-700 data-disabled:bg-gray-600" : "bg-gray-800 data-disabled:bg-gray-600",
                    "relative ml-4 inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-black transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-sky-500"
                )}
                onCheckedChange={onChange}
            >
                <Switch.Thumb
                    className={cn(
                        value ? "translate-x-5" : "translate-x-0",
                        "block size-5 rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out data-disabled:bg-gray-600 dark:bg-gray-300"
                    )}
                />
            </Switch.Root>
        </div>
    );
};

export default ToggleSwitch;
