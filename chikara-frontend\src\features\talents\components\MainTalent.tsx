import { cn } from "@/lib/utils";
import { displayMissingIcon } from "@/helpers/displayMissingIcon";
import type React from "react";
import type { TalentInfo, UserTalent } from "@/features/talents/types/talents";

interface MainTalentProps {
    talent: TalentInfo;
    selectedTalent?: TalentInfo | null;
    setSelectedTalent: (talent?: TalentInfo | null) => void;
    isTalentDisabled?: boolean;
    talentUnlocked?: UserTalent | null;
}

export default function MainTalent({ talent, selectedTalent, setSelectedTalent, isTalentDisabled, talentUnlocked }: MainTalentProps) {
    const handleClick = (e: React.MouseEvent) => {
        if (selectedTalent?.name === talent.name) {
            setSelectedTalent();
        } else {
            setSelectedTalent(talent);
        }
    };

    return (
        <div
            className={cn(
                isTalentDisabled && "brightness-50 grayscale",
                "relative m-auto flex h-[55%] w-auto cursor-pointer select-none shadow-2xl"
            )}
            onClick={(e) => handleClick(e)}
        >
            {/* <p className="text-white text-stroke-s-md absolute "> {talent.name}</p> */}
            {talent?.talentType === "passive" && (
                <img
                    src={displayMissingIcon(talent.image)}
                    alt=""
                    className={cn(
                        "m-auto max-h-full rounded-md border-2 border-black ring-2 ring-blue-500",
                        selectedTalent?.name === talent.name && "contrast-125 saturate-150"
                    )}
                />
            )}
            {talent?.talentType === "ability" && (
                <img
                    src={displayMissingIcon(talent.image)}
                    alt=""
                    className={cn(
                        "m-auto max-h-[105%] rounded-full border-2 border-black outline-solid outline-2 outline-slate-800 ring-4 ring-yellow-500",
                        selectedTalent?.name === talent.name && "contrast-125 saturate-150"
                    )}
                />
            )}
            {talentUnlocked?.level !== talent?.maxPoints && (
                <div
                    className={cn(
                        "absolute h-[103%] w-full bg-gray-400 opacity-50",
                        talent?.talentType === "ability" ? "rounded-full" : "rounded-md"
                    )}
                ></div>
            )}

            {talent?.talentType === "passive" && (
                <div className="-bottom-3 absolute left-[30%] flex h-5 w-8 rounded-md border border-slate-300 bg-slate-700 font-lili text-white text-xs tracking-widest md:h-6 md:w-10 md:text-sm">
                    <p className="m-auto">
                        {talentUnlocked ? talentUnlocked.level : 0}/{talent?.maxPoints}
                    </p>
                </div>
            )}
        </div>
    );
}
