import Spinner from "@/components/Spinners/Spinner";
import Meteors from "@/components/ui/meteors";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { cn } from "@/lib/utils";
import { ArrowLeft } from "lucide-react";
import posthog from "posthog-js";
import { useEffect, useState } from "react";
import { useNormalStore } from "../../app/store/stores";
import terraformerLogo from "./terraformerlogo.png";

export default function Arcade() {
    const [gameIsComplete, setGameIsComplete] = useState(false);
    const [terraformerOpen, setTerraformerOpen] = useState(false);
    const [iframeLoading, setIframeLoading] = useState(false);

    const { setIframeActive } = useNormalStore();
    const { data: currentUser } = useFetchCurrentUser();

    async function markTerraformerComplete() {
        // TODO - Implement this
    }

    useEffect(() => {
        const handleMessage = (event: MessageEvent) => {
            if (event.origin === "https://terraformer.vercel.app") {
                const receivedData = event.data;
                if (receivedData.message === "gameComplete") {
                    setGameIsComplete(true);
                    window.removeEventListener("message", handleMessage);
                }
            }
        };

        window.addEventListener("message", handleMessage);

        return () => {
            window.removeEventListener("message", handleMessage);
        };
    }, []);

    useEffect(() => {
        if (gameIsComplete) {
            markTerraformerComplete();
            posthog.capture("terraformer_completed", {});
        }
    }, [gameIsComplete]);

    const handleTerraformerOpen = () => {
        if (currentUser?.level && currentUser.level >= 15) {
            setIframeActive(true);
            setTerraformerOpen(true);
            setIframeLoading(true);
        }
    };

    if (!terraformerOpen) {
        return (
            <div className="mt-4 flex size-full flex-col items-center gap-4 p-5 md:p-0">
                <button
                    type="button"
                    className="group relative mx-auto flex h-24 w-full cursor-pointer select-none flex-col items-center justify-center overflow-hidden rounded-lg border border-gray-700 bg-background hover:ring-2 active:scale-[97%] md:h-32 md:w-3/5"
                    onClick={() => handleTerraformerOpen()}
                >
                    <Meteors number={20} />
                    <div className="flex flex-row-reverse gap-4 pr-6 md:flex-row md:gap-5 md:pl-20">
                        <div className="my-auto">
                            {" "}
                            <p className="z-10 whitespace-pre-wrap text-center font-medium text-3xl text-black text-stroke-md tracking-tighter dark:text-custom-yellow">
                                TERRAFORMER
                            </p>
                            {(currentUser?.level || 0) < 15 ? (
                                <p className="z-10 font-medium text-red-500 text-xl transition-transform group-hover:scale-[1.2] group-hover:text-red-400 dark:text-stroke-s-sm">
                                    Unlocked at level 15
                                </p>
                            ) : (
                                <p className="z-10 font-medium text-2xl text-blue-500 transition-transform group-hover:scale-[1.2] group-hover:text-sky-400 dark:text-stroke-s-sm">
                                    Play Now <span className="text-gray-400 text-lg">(Demo)</span>
                                </p>
                            )}
                        </div>
                        <img
                            className="z-10 my-auto h-24 w-auto rounded-full group-hover:brightness-110 md:h-36"
                            src={terraformerLogo}
                            alt=""
                        />
                    </div>
                    <div className="pointer-events-none absolute inset-0 h-full bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.3),rgba(255,255,255,0))]" />
                </button>

                {/* <button
          onClick={() => handleFaceminerOpen()}
          type="button"
          className="bg-background group relative flex h-24 w-full md:w-3/5 mx-auto cursor-pointer flex-col items-center border-gray-700 justify-center overflow-hidden rounded-lg border md:h-32 hover:ring-2 active:scale-[97%] select-none"
        >
          <div className="flex gap-4 md:gap-5 md:pl-20 md:flex-row flex-row-reverse pr-6 glitch-screen">
            <div className="my-auto">
              {" "}
              <p className="z-10 text-yellow-500 whitespace-pre-wrap text-center text-3xl font-medium tracking-tighter  text-stroke-md">
                FACEMINER
              </p>
              <p className="text-blue-500 z-10 group-hover:text-sky-400 dark:text-stroke-s-sm text-2xl font-medium group-hover:scale-[1.2] transition-transform">
                Play Now <span className="text-gray-400 text-lg">(Demo)</span>
              </p>
              <p className="text-red-400">Desktop Only</p>
            </div>
            <div className="relative h-fit w-fit ">
              <div className="tv-static w-full h-full opacity-10 z-50 scale-[0.85] md:scale-[0.8]"></div>
              <img
                className="h-24 md:h-36 w-auto my-auto group-hover:brightness-110 z-10 md:p-4 p-2"
                src="https://cdn.akamai.steamstatic.com/steam/apps/2276980/capsule_616x353.jpg?t=1713258282"
                alt=""
              />
            </div>
          </div>

          <div className="pointer-events-none absolute inset-0 h-full bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.3),rgba(255,255,255,0))]" />
        </button> */}
                <button
                    disabled
                    type="button"
                    className="relative mx-auto flex h-24 w-full select-none flex-col items-center justify-center overflow-hidden rounded-lg border border-gray-700 bg-background grayscale md:h-32 md:w-1/2"
                >
                    <p className="z-10 whitespace-pre-wrap text-center font-medium text-4xl text-black tracking-tighter dark:text-gray-400 dark:text-stroke-s-sm">
                        COMING SOON
                    </p>
                    <div className="pointer-events-none absolute inset-0 h-full bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.3),rgba(255,255,255,0))]" />
                </button>
                <button
                    disabled
                    type="button"
                    className="relative mx-auto flex h-24 w-full select-none flex-col items-center justify-center overflow-hidden rounded-lg border border-gray-700 bg-background grayscale md:h-32 md:w-1/2"
                >
                    <p className="z-10 whitespace-pre-wrap text-center font-medium text-4xl text-black tracking-tighter dark:text-gray-400 dark:text-stroke-s-sm">
                        COMING SOON
                    </p>
                    <div className="pointer-events-none absolute inset-0 h-full bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.3),rgba(255,255,255,0))]" />
                </button>
            </div>
        );
    }

    return (
        <>
            {terraformerOpen && (
                <GameIframe
                    gameURL="https://terraformer.vercel.app/?chikara=true"
                    iframeLoading={iframeLoading}
                    setIframeActive={setIframeActive}
                    setGameOpen={setTerraformerOpen}
                    setIframeLoading={setIframeLoading}
                    title="Terraformer"
                />
            )}
            {/* {faceminerOpen && (
        <GameIframe
          gameURL="https://faceminer.vercel.app/"
          iframeLoading={iframeLoading}
          setIframeActive={setIframeActive}
          setGameOpen={setFaceMinerOpen}
          setIframeLoading={setIframeLoading}
        />
      )} */}
        </>
    );
}

// https://faceminer.vercel.app/

interface GameIframeProps {
    gameURL: string;
    iframeLoading: boolean;
    setIframeActive: (active: boolean) => void;
    setGameOpen: (open: boolean) => void;
    setIframeLoading: (loading: boolean) => void;
    title?: string | null;
}

const GameIframe = ({ gameURL, iframeLoading, setIframeActive, setGameOpen, setIframeLoading, title = null }: GameIframeProps) => {
    const isMobile = useCheckMobileScreen();
    const iframeMobileStyle = {
        border: "0px #ffffff none",
        height: "calc(100dvh - 3.75rem)",
        width: "100%",
    };

    const iframeDesktopStyle = {
        border: "2px solid rgb(55 65 81)",
        borderRadius: "25px",
        height: "calc(100vh - 160px)",
        width: "100%",
    };
    return (
        <div className="relative md:mx-auto md:max-w-(--breakpoint-2xl)">
            {iframeLoading && (
                <div className="mt-24 flex size-full">
                    <div className="m-auto">
                        <Spinner />
                    </div>
                </div>
            )}
            <button
                type="button"
                className={cn(
                    iframeLoading ? "md:hidden" : "md:inline-flex",
                    "mt-0 mb-2 hidden items-center rounded-md border border-transparent bg-indigo-700 px-6 py-2 text-md text-white shadow-xs hover:bg-indigo-800 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:inline-flex"
                )}
                onClick={() => {
                    setIframeActive(false);
                    setGameOpen(false);
                }}
            >
                <ArrowLeft className="mr-3 size-5" />
                Back
            </button>
            <iframe
                allowFullScreen
                className={cn(iframeLoading && "hidden", "fixed top-16 left-0 md:relative md:top-0")}
                title={title}
                src={gameURL}
                style={isMobile ? iframeMobileStyle : iframeDesktopStyle}
                name="gameIframe"
                onLoad={() => setIframeLoading(false)}
            ></iframe>
        </div>
    );
};
