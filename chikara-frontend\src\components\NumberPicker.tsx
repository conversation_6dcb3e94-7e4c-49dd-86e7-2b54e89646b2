import { cn } from "@/lib/utils";

interface NumberPickerProps {
    currentValue: number;
    handleInputChange: (_value: number, _valueId?: string | null) => void;
    valueId?: string | null;
    state?: Record<string, number>;
    statEnergyCost?: number;
    maxValue: number;
    minValue?: number;
    valueChangeAmount?: number;
    showPlus?: boolean;
    textColour?: string;
    className?: string;
}

const NumberPicker = ({
    currentValue,
    handleInputChange,
    valueId = null,
    state,
    statEnergyCost,
    maxValue,
    minValue = 0,
    valueChangeAmount = 1,
    showPlus = false,
    textColour,
    className,
}: NumberPickerProps) => {
    const decrement = () => {
        if (currentValue <= minValue) {
            return;
        }
        const value = currentValue - valueChangeAmount;

        handleInputChange(value, valueId);
    };

    const increment = () => {
        if (currentValue >= maxValue) {
            return;
        }
        const value = currentValue + valueChangeAmount;

        handleInputChange(value, valueId);
    };

    return (
        <>
            <div className={cn("relative mx-auto flex h-8 w-24 flex-row rounded-md border border-gray-500", className)}>
                <button
                    data-action="decrement"
                    className="flex h-full w-28 cursor-pointer rounded-l-[4.8px] border-gray-500 border-r bg-[#323859] font-semibold text-white hover:bg-[#545b7e] focus:outline-hidden"
                    onClick={decrement}
                >
                    <span className="m-auto mb-0.5 text-lg">-</span>
                </button>
                <input
                    readOnly
                    type="hidden"
                    className="border-gray-500 p-1 text-center text-xs focus:outline-hidden md:p-2 md:text-base"
                    name="custom-input-number"
                />
                <div
                    className={cn(
                        "flex w-32 cursor-default items-center justify-center bg-[#12172F] text-sm md:text-base",
                        textColour ? textColour : "text-slate-200"
                    )}
                >
                    <span>
                        {currentValue === 0 || !showPlus ? null : "+"}
                        {currentValue / valueChangeAmount}
                    </span>
                </div>

                <button
                    data-action="increment"
                    className="flex h-full w-28 cursor-pointer rounded-r-[4.8px] border-gray-500 border-l bg-[#323859] font-semibold text-white hover:bg-[#545b7e] focus:outline-hidden"
                    onClick={increment}
                >
                    <span className="m-auto text-lg">+</span>
                </button>
            </div>
            {valueId && state && statEnergyCost && (
                <div className="flex">
                    <p className="mx-auto mt-0.5 h-3 text-gray-900 dark:text-blue-200">
                        {currentValue === 0 ? null : `(${statEnergyCost * state[valueId]} energy)`}
                    </p>
                </div>
            )}
        </>
    );
};

export default NumberPicker;
