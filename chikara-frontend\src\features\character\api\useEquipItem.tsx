import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { api } from "@/helpers/api";
import type { InventoryItem } from "@/types/item";
import type { User } from "@/types/user";

interface EquipItemVariables {
    currentUser: User;
    userItem: InventoryItem;
}

interface EquipItemInput {
    userItemId: number;
    _userItem: InventoryItem;
}

const useEquipItem = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.user.equipItem.mutationOptions({
            onMutate: async (input: EquipItemInput) => {
                // Extract userItem from the context we'll pass
                const userItem = input._userItem;

                await queryClient.cancelQueries({
                    queryKey: api.user.getEquippedItems.key(),
                });

                const previousEquippedItems = queryClient.getQueryData(api.user.getEquippedItems.key());

                queryClient.setQueryData(api.user.getEquippedItems.key(), (old: any) => {
                    const optimisticUpdate = { ...old };
                    return { ...optimisticUpdate, [userItem.item.itemType]: userItem };
                });

                return { previousEquippedItems };
            },
            onError: (err: any, variables: EquipItemInput, context: any) => {
                queryClient.setQueryData(api.user.getEquippedItems.key(), context.previousEquippedItems);
                toast.error(err.message || "An error occurred");
            },
            onSettled: async () => {
                await queryClient.invalidateQueries({
                    queryKey: api.user.getEquippedItems.key(),
                });
            },
            onSuccess: async () => {
                await queryClient.invalidateQueries({
                    queryKey: api.user.getEquippedItems.key(),
                });
                toast.success("Item equipped!");
            },
        })
    );

    // Wrapper function to handle the ORPC input format and user state validation
    const equipItem = (variables: EquipItemVariables) => {
        const { currentUser, userItem } = variables;

        // Client-side validation (same as before)
        if (currentUser?.hospitalisedUntil && currentUser.hospitalisedUntil > 0) {
            toast.error("Can't equip items while hospitalised!");
            return Promise.reject(new Error("Can't equip items while hospitalised!"));
        }
        if (currentUser?.jailedUntil && currentUser.jailedUntil > 0) {
            toast.error("Can't equip items while jailed!");
            return Promise.reject(new Error("Can't equip items while jailed!"));
        }

        // Call the ORPC mutation with the correct input format
        // Pass userItem as a private property for optimistic updates
        return mutation.mutate({
            userItemId: userItem.id,
            _userItem: userItem, // Pass userItem for optimistic updates
        });
    };

    return {
        equipItem: { ...mutation, mutate: equipItem },
    };
};

export default useEquipItem;
