const Tt=()=>{};var Se={};/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const He=function(t){const e=[];let n=0;for(let r=0;r<t.length;r++){let s=t.charCodeAt(r);s<128?e[n++]=s:s<2048?(e[n++]=s>>6|192,e[n++]=s&63|128):(s&64512)===55296&&r+1<t.length&&(t.charCodeAt(r+1)&64512)===56320?(s=65536+((s&1023)<<10)+(t.charCodeAt(++r)&1023),e[n++]=s>>18|240,e[n++]=s>>12&63|128,e[n++]=s>>6&63|128,e[n++]=s&63|128):(e[n++]=s>>12|224,e[n++]=s>>6&63|128,e[n++]=s&63|128)}return e},At=function(t){const e=[];let n=0,r=0;for(;n<t.length;){const s=t[n++];if(s<128)e[r++]=String.fromCharCode(s);else if(s>191&&s<224){const i=t[n++];e[r++]=String.fromCharCode((s&31)<<6|i&63)}else if(s>239&&s<365){const i=t[n++],a=t[n++],o=t[n++],c=((s&7)<<18|(i&63)<<12|(a&63)<<6|o&63)-65536;e[r++]=String.fromCharCode(55296+(c>>10)),e[r++]=String.fromCharCode(56320+(c&1023))}else{const i=t[n++],a=t[n++];e[r++]=String.fromCharCode((s&15)<<12|(i&63)<<6|a&63)}}return e.join("")},Ke={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:typeof atob=="function",encodeByteArray(t,e){if(!Array.isArray(t))throw Error("encodeByteArray takes an array as a parameter");this.init_();const n=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,r=[];for(let s=0;s<t.length;s+=3){const i=t[s],a=s+1<t.length,o=a?t[s+1]:0,c=s+2<t.length,l=c?t[s+2]:0,u=i>>2,d=(i&3)<<4|o>>4;let v=(o&15)<<2|l>>6,B=l&63;c||(B=64,a||(v=64)),r.push(n[u],n[d],n[v],n[B])}return r.join("")},encodeString(t,e){return this.HAS_NATIVE_SUPPORT&&!e?btoa(t):this.encodeByteArray(He(t),e)},decodeString(t,e){return this.HAS_NATIVE_SUPPORT&&!e?atob(t):At(this.decodeStringToByteArray(t,e))},decodeStringToByteArray(t,e){this.init_();const n=e?this.charToByteMapWebSafe_:this.charToByteMap_,r=[];for(let s=0;s<t.length;){const i=n[t.charAt(s++)],o=s<t.length?n[t.charAt(s)]:0;++s;const l=s<t.length?n[t.charAt(s)]:64;++s;const d=s<t.length?n[t.charAt(s)]:64;if(++s,i==null||o==null||l==null||d==null)throw new kt;const v=i<<2|o>>4;if(r.push(v),l!==64){const B=o<<4&240|l>>2;if(r.push(B),d!==64){const St=l<<6&192|d;r.push(St)}}}return r},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let t=0;t<this.ENCODED_VALS.length;t++)this.byteToCharMap_[t]=this.ENCODED_VALS.charAt(t),this.charToByteMap_[this.byteToCharMap_[t]]=t,this.byteToCharMapWebSafe_[t]=this.ENCODED_VALS_WEBSAFE.charAt(t),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[t]]=t,t>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(t)]=t,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(t)]=t)}}};class kt extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}const Rt=function(t){const e=He(t);return Ke.encodeByteArray(e,!0)},Ve=function(t){return Rt(t).replace(/\./g,"")},Dt=function(t){try{return Ke.decodeString(t,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Ot(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("Unable to locate global object.")}/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Nt=()=>Ot().__FIREBASE_DEFAULTS__,Mt=()=>{if(typeof process>"u"||typeof Se>"u")return;const t=Se.__FIREBASE_DEFAULTS__;if(t)return JSON.parse(t)},Lt=()=>{if(typeof document>"u")return;let t;try{t=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch{return}const e=t&&Dt(t[1]);return e&&JSON.parse(e)},Pt=()=>{try{return Tt()||Nt()||Mt()||Lt()}catch(t){console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${t}`);return}},We=()=>{var t;return(t=Pt())===null||t===void 0?void 0:t.config};/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let Bt=class{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((e,n)=>{this.resolve=e,this.reject=n})}wrapCallback(e){return(n,r)=>{n?this.reject(n):this.resolve(r),typeof e=="function"&&(this.promise.catch(()=>{}),e.length===1?e(n):e(n,r))}}};function qe(){try{return typeof indexedDB=="object"}catch{return!1}}function ze(){return new Promise((t,e)=>{try{let n=!0;const r="validate-browser-context-for-indexeddb-analytics-module",s=self.indexedDB.open(r);s.onsuccess=()=>{s.result.close(),n||self.indexedDB.deleteDatabase(r),t(!0)},s.onupgradeneeded=()=>{n=!1},s.onerror=()=>{var i;e(((i=s.error)===null||i===void 0?void 0:i.message)||"")}}catch(n){e(n)}})}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const $t="FirebaseError";class O extends Error{constructor(e,n,r){super(n),this.code=e,this.customData=r,this.name=$t,Object.setPrototypeOf(this,O.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,K.prototype.create)}}class K{constructor(e,n,r){this.service=e,this.serviceName=n,this.errors=r}create(e,...n){const r=n[0]||{},s=`${this.service}/${e}`,i=this.errors[e],a=i?Ut(i,r):"Error",o=`${this.serviceName}: ${a} (${s}).`;return new O(s,o,r)}}function Ut(t,e){return t.replace(xt,(n,r)=>{const s=e[r];return s!=null?String(s):`<${r}?>`})}const xt=/\{\$([^}]+)}/g;function ie(t,e){if(t===e)return!0;const n=Object.keys(t),r=Object.keys(e);for(const s of n){if(!r.includes(s))return!1;const i=t[s],a=e[s];if(Te(i)&&Te(a)){if(!ie(i,a))return!1}else if(i!==a)return!1}for(const s of r)if(!n.includes(s))return!1;return!0}function Te(t){return t!==null&&typeof t=="object"}/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Ge(t){return t&&t._delegate?t._delegate:t}class S{constructor(e,n,r){this.name=e,this.instanceFactory=n,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const E="[DEFAULT]";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Ft{constructor(e,n){this.name=e,this.container=n,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(e){const n=this.normalizeInstanceIdentifier(e);if(!this.instancesDeferred.has(n)){const r=new Bt;if(this.instancesDeferred.set(n,r),this.isInitialized(n)||this.shouldAutoInitialize())try{const s=this.getOrInitializeService({instanceIdentifier:n});s&&r.resolve(s)}catch{}}return this.instancesDeferred.get(n).promise}getImmediate(e){var n;const r=this.normalizeInstanceIdentifier(e?.identifier),s=(n=e?.optional)!==null&&n!==void 0?n:!1;if(this.isInitialized(r)||this.shouldAutoInitialize())try{return this.getOrInitializeService({instanceIdentifier:r})}catch(i){if(s)return null;throw i}else{if(s)return null;throw Error(`Service ${this.name} is not available`)}}getComponent(){return this.component}setComponent(e){if(e.name!==this.name)throw Error(`Mismatching Component ${e.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=e,!!this.shouldAutoInitialize()){if(Ht(e))try{this.getOrInitializeService({instanceIdentifier:E})}catch{}for(const[n,r]of this.instancesDeferred.entries()){const s=this.normalizeInstanceIdentifier(n);try{const i=this.getOrInitializeService({instanceIdentifier:s});r.resolve(i)}catch{}}}}clearInstance(e=E){this.instancesDeferred.delete(e),this.instancesOptions.delete(e),this.instances.delete(e)}async delete(){const e=Array.from(this.instances.values());await Promise.all([...e.filter(n=>"INTERNAL"in n).map(n=>n.INTERNAL.delete()),...e.filter(n=>"_delete"in n).map(n=>n._delete())])}isComponentSet(){return this.component!=null}isInitialized(e=E){return this.instances.has(e)}getOptions(e=E){return this.instancesOptions.get(e)||{}}initialize(e={}){const{options:n={}}=e,r=this.normalizeInstanceIdentifier(e.instanceIdentifier);if(this.isInitialized(r))throw Error(`${this.name}(${r}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);const s=this.getOrInitializeService({instanceIdentifier:r,options:n});for(const[i,a]of this.instancesDeferred.entries()){const o=this.normalizeInstanceIdentifier(i);r===o&&a.resolve(s)}return s}onInit(e,n){var r;const s=this.normalizeInstanceIdentifier(n),i=(r=this.onInitCallbacks.get(s))!==null&&r!==void 0?r:new Set;i.add(e),this.onInitCallbacks.set(s,i);const a=this.instances.get(s);return a&&e(a,s),()=>{i.delete(e)}}invokeOnInitCallbacks(e,n){const r=this.onInitCallbacks.get(n);if(r)for(const s of r)try{s(e,n)}catch{}}getOrInitializeService({instanceIdentifier:e,options:n={}}){let r=this.instances.get(e);if(!r&&this.component&&(r=this.component.instanceFactory(this.container,{instanceIdentifier:jt(e),options:n}),this.instances.set(e,r),this.instancesOptions.set(e,n),this.invokeOnInitCallbacks(r,e),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,e,r)}catch{}return r||null}normalizeInstanceIdentifier(e=E){return this.component?this.component.multipleInstances?e:E:e}shouldAutoInitialize(){return!!this.component&&this.component.instantiationMode!=="EXPLICIT"}}function jt(t){return t===E?void 0:t}function Ht(t){return t.instantiationMode==="EAGER"}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Kt{constructor(e){this.name=e,this.providers=new Map}addComponent(e){const n=this.getProvider(e.name);if(n.isComponentSet())throw new Error(`Component ${e.name} has already been registered with ${this.name}`);n.setComponent(e)}addOrOverwriteComponent(e){this.getProvider(e.name).isComponentSet()&&this.providers.delete(e.name),this.addComponent(e)}getProvider(e){if(this.providers.has(e))return this.providers.get(e);const n=new Ft(e,this);return this.providers.set(e,n),n}getProviders(){return Array.from(this.providers.values())}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */var h;(function(t){t[t.DEBUG=0]="DEBUG",t[t.VERBOSE=1]="VERBOSE",t[t.INFO=2]="INFO",t[t.WARN=3]="WARN",t[t.ERROR=4]="ERROR",t[t.SILENT=5]="SILENT"})(h||(h={}));const Vt={debug:h.DEBUG,verbose:h.VERBOSE,info:h.INFO,warn:h.WARN,error:h.ERROR,silent:h.SILENT},Wt=h.INFO,qt={[h.DEBUG]:"log",[h.VERBOSE]:"log",[h.INFO]:"info",[h.WARN]:"warn",[h.ERROR]:"error"},zt=(t,e,...n)=>{if(e<t.logLevel)return;const r=new Date().toISOString(),s=qt[e];if(s)console[s](`[${r}]  ${t.name}:`,...n);else throw new Error(`Attempted to log a message with an invalid logType (value: ${e})`)};class Gt{constructor(e){this.name=e,this._logLevel=Wt,this._logHandler=zt,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in h))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel=typeof e=="string"?Vt[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if(typeof e!="function")throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,h.DEBUG,...e),this._logHandler(this,h.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,h.VERBOSE,...e),this._logHandler(this,h.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,h.INFO,...e),this._logHandler(this,h.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,h.WARN,...e),this._logHandler(this,h.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,h.ERROR,...e),this._logHandler(this,h.ERROR,...e)}}const Jt=(t,e)=>e.some(n=>t instanceof n);let Ae,ke;function Yt(){return Ae||(Ae=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])}function Qt(){return ke||(ke=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])}const Je=new WeakMap,ae=new WeakMap,Ye=new WeakMap,z=new WeakMap,de=new WeakMap;function Xt(t){const e=new Promise((n,r)=>{const s=()=>{t.removeEventListener("success",i),t.removeEventListener("error",a)},i=()=>{n(b(t.result)),s()},a=()=>{r(t.error),s()};t.addEventListener("success",i),t.addEventListener("error",a)});return e.then(n=>{n instanceof IDBCursor&&Je.set(n,t)}).catch(()=>{}),de.set(e,t),e}function Zt(t){if(ae.has(t))return;const e=new Promise((n,r)=>{const s=()=>{t.removeEventListener("complete",i),t.removeEventListener("error",a),t.removeEventListener("abort",a)},i=()=>{n(),s()},a=()=>{r(t.error||new DOMException("AbortError","AbortError")),s()};t.addEventListener("complete",i),t.addEventListener("error",a),t.addEventListener("abort",a)});ae.set(t,e)}let oe={get(t,e,n){if(t instanceof IDBTransaction){if(e==="done")return ae.get(t);if(e==="objectStoreNames")return t.objectStoreNames||Ye.get(t);if(e==="store")return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return b(t[e])},set(t,e,n){return t[e]=n,!0},has(t,e){return t instanceof IDBTransaction&&(e==="done"||e==="store")?!0:e in t}};function en(t){oe=t(oe)}function tn(t){return t===IDBDatabase.prototype.transaction&&!("objectStoreNames"in IDBTransaction.prototype)?function(e,...n){const r=t.call(G(this),e,...n);return Ye.set(r,e.sort?e.sort():[e]),b(r)}:Qt().includes(t)?function(...e){return t.apply(G(this),e),b(Je.get(this))}:function(...e){return b(t.apply(G(this),e))}}function nn(t){return typeof t=="function"?tn(t):(t instanceof IDBTransaction&&Zt(t),Jt(t,Yt())?new Proxy(t,oe):t)}function b(t){if(t instanceof IDBRequest)return Xt(t);if(z.has(t))return z.get(t);const e=nn(t);return e!==t&&(z.set(t,e),de.set(e,t)),e}const G=t=>de.get(t);function V(t,e,{blocked:n,upgrade:r,blocking:s,terminated:i}={}){const a=indexedDB.open(t,e),o=b(a);return r&&a.addEventListener("upgradeneeded",c=>{r(b(a.result),c.oldVersion,c.newVersion,b(a.transaction),c)}),n&&a.addEventListener("blocked",c=>n(c.oldVersion,c.newVersion,c)),o.then(c=>{i&&c.addEventListener("close",()=>i()),s&&c.addEventListener("versionchange",l=>s(l.oldVersion,l.newVersion,l))}).catch(()=>{}),o}function J(t,{blocked:e}={}){const n=indexedDB.deleteDatabase(t);return e&&n.addEventListener("blocked",r=>e(r.oldVersion,r)),b(n).then(()=>{})}const rn=["get","getKey","getAll","getAllKeys","count"],sn=["put","add","delete","clear"],Y=new Map;function Re(t,e){if(!(t instanceof IDBDatabase&&!(e in t)&&typeof e=="string"))return;if(Y.get(e))return Y.get(e);const n=e.replace(/FromIndex$/,""),r=e!==n,s=sn.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!(s||rn.includes(n)))return;const i=async function(a,...o){const c=this.transaction(a,s?"readwrite":"readonly");let l=c.store;return r&&(l=l.index(o.shift())),(await Promise.all([l[n](...o),s&&c.done]))[0]};return Y.set(e,i),i}en(t=>({...t,get:(e,n,r)=>Re(e,n)||t.get(e,n,r),has:(e,n)=>!!Re(e,n)||t.has(e,n)}));/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class an{constructor(e){this.container=e}getPlatformInfoString(){return this.container.getProviders().map(n=>{if(on(n)){const r=n.getImmediate();return`${r.library}/${r.version}`}else return null}).filter(n=>n).join(" ")}}function on(t){const e=t.getComponent();return e?.type==="VERSION"}const ce="@firebase/app",De="0.13.2";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const w=new Gt("@firebase/app"),cn="@firebase/app-compat",ln="@firebase/analytics-compat",un="@firebase/analytics",hn="@firebase/app-check-compat",dn="@firebase/app-check",fn="@firebase/auth",pn="@firebase/auth-compat",gn="@firebase/database",mn="@firebase/data-connect",bn="@firebase/database-compat",wn="@firebase/functions",yn="@firebase/functions-compat",_n="@firebase/installations",In="@firebase/installations-compat",vn="@firebase/messaging",En="@firebase/messaging-compat",Cn="@firebase/performance",Sn="@firebase/performance-compat",Tn="@firebase/remote-config",An="@firebase/remote-config-compat",kn="@firebase/storage",Rn="@firebase/storage-compat",Dn="@firebase/firestore",On="@firebase/ai",Nn="@firebase/firestore-compat",Mn="firebase";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const le="[DEFAULT]",Ln={[ce]:"fire-core",[cn]:"fire-core-compat",[un]:"fire-analytics",[ln]:"fire-analytics-compat",[dn]:"fire-app-check",[hn]:"fire-app-check-compat",[fn]:"fire-auth",[pn]:"fire-auth-compat",[gn]:"fire-rtdb",[mn]:"fire-data-connect",[bn]:"fire-rtdb-compat",[wn]:"fire-fn",[yn]:"fire-fn-compat",[_n]:"fire-iid",[In]:"fire-iid-compat",[vn]:"fire-fcm",[En]:"fire-fcm-compat",[Cn]:"fire-perf",[Sn]:"fire-perf-compat",[Tn]:"fire-rc",[An]:"fire-rc-compat",[kn]:"fire-gcs",[Rn]:"fire-gcs-compat",[Dn]:"fire-fst",[Nn]:"fire-fst-compat",[On]:"fire-vertex","fire-js":"fire-js",[Mn]:"fire-js-all"};/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const U=new Map,Pn=new Map,ue=new Map;function Oe(t,e){try{t.container.addComponent(e)}catch(n){w.debug(`Component ${e.name} failed to register with FirebaseApp ${t.name}`,n)}}function D(t){const e=t.name;if(ue.has(e))return w.debug(`There were multiple attempts to register component ${e}.`),!1;ue.set(e,t);for(const n of U.values())Oe(n,t);for(const n of Pn.values())Oe(n,t);return!0}function fe(t,e){const n=t.container.getProvider("heartbeat").getImmediate({optional:!0});return n&&n.triggerHeartbeat(),t.container.getProvider(e)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Bn={"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."},I=new K("app","Firebase",Bn);/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class $n{constructor(e,n,r){this._isDeleted=!1,this._options=Object.assign({},e),this._config=Object.assign({},n),this._name=n.name,this._automaticDataCollectionEnabled=n.automaticDataCollectionEnabled,this._container=r,this.container.addComponent(new S("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(e){this.checkDestroyed(),this._automaticDataCollectionEnabled=e}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(e){this._isDeleted=e}checkDestroyed(){if(this.isDeleted)throw I.create("app-deleted",{appName:this._name})}}function Qe(t,e={}){let n=t;typeof e!="object"&&(e={name:e});const r=Object.assign({name:le,automaticDataCollectionEnabled:!0},e),s=r.name;if(typeof s!="string"||!s)throw I.create("bad-app-name",{appName:String(s)});if(n||(n=We()),!n)throw I.create("no-options");const i=U.get(s);if(i){if(ie(n,i.options)&&ie(r,i.config))return i;throw I.create("duplicate-app",{appName:s})}const a=new Kt(s);for(const c of ue.values())a.addComponent(c);const o=new $n(n,r,a);return U.set(s,o),o}function Un(t=le){const e=U.get(t);if(!e&&t===le&&We())return Qe();if(!e)throw I.create("no-app",{appName:t});return e}function R(t,e,n){var r;let s=(r=Ln[t])!==null&&r!==void 0?r:t;n&&(s+=`-${n}`);const i=s.match(/\s|\//),a=e.match(/\s|\//);if(i||a){const o=[`Unable to register library "${s}" with version "${e}":`];i&&o.push(`library name "${s}" contains illegal characters (whitespace or "/")`),i&&a&&o.push("and"),a&&o.push(`version name "${e}" contains illegal characters (whitespace or "/")`),w.warn(o.join(" "));return}D(new S(`${s}-version`,()=>({library:s,version:e}),"VERSION"))}/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const xn="firebase-heartbeat-database",Fn=1,P="firebase-heartbeat-store";let Q=null;function Xe(){return Q||(Q=V(xn,Fn,{upgrade:(t,e)=>{switch(e){case 0:try{t.createObjectStore(P)}catch(n){console.warn(n)}}}}).catch(t=>{throw I.create("idb-open",{originalErrorMessage:t.message})})),Q}async function jn(t){try{const n=(await Xe()).transaction(P),r=await n.objectStore(P).get(Ze(t));return await n.done,r}catch(e){if(e instanceof O)w.warn(e.message);else{const n=I.create("idb-get",{originalErrorMessage:e?.message});w.warn(n.message)}}}async function Ne(t,e){try{const r=(await Xe()).transaction(P,"readwrite");await r.objectStore(P).put(e,Ze(t)),await r.done}catch(n){if(n instanceof O)w.warn(n.message);else{const r=I.create("idb-set",{originalErrorMessage:n?.message});w.warn(r.message)}}}function Ze(t){return`${t.name}!${t.options.appId}`}/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Hn=1024,Kn=30;class Vn{constructor(e){this.container=e,this._heartbeatsCache=null;const n=this.container.getProvider("app").getImmediate();this._storage=new qn(n),this._heartbeatsCachePromise=this._storage.read().then(r=>(this._heartbeatsCache=r,r))}async triggerHeartbeat(){var e,n;try{const s=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),i=Me();if(((e=this._heartbeatsCache)===null||e===void 0?void 0:e.heartbeats)==null&&(this._heartbeatsCache=await this._heartbeatsCachePromise,((n=this._heartbeatsCache)===null||n===void 0?void 0:n.heartbeats)==null)||this._heartbeatsCache.lastSentHeartbeatDate===i||this._heartbeatsCache.heartbeats.some(a=>a.date===i))return;if(this._heartbeatsCache.heartbeats.push({date:i,agent:s}),this._heartbeatsCache.heartbeats.length>Kn){const a=zn(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(a,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(r){w.warn(r)}}async getHeartbeatsHeader(){var e;try{if(this._heartbeatsCache===null&&await this._heartbeatsCachePromise,((e=this._heartbeatsCache)===null||e===void 0?void 0:e.heartbeats)==null||this._heartbeatsCache.heartbeats.length===0)return"";const n=Me(),{heartbeatsToSend:r,unsentEntries:s}=Wn(this._heartbeatsCache.heartbeats),i=Ve(JSON.stringify({version:2,heartbeats:r}));return this._heartbeatsCache.lastSentHeartbeatDate=n,s.length>0?(this._heartbeatsCache.heartbeats=s,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),i}catch(n){return w.warn(n),""}}}function Me(){return new Date().toISOString().substring(0,10)}function Wn(t,e=Hn){const n=[];let r=t.slice();for(const s of t){const i=n.find(a=>a.agent===s.agent);if(i){if(i.dates.push(s.date),Le(n)>e){i.dates.pop();break}}else if(n.push({agent:s.agent,dates:[s.date]}),Le(n)>e){n.pop();break}r=r.slice(1)}return{heartbeatsToSend:n,unsentEntries:r}}class qn{constructor(e){this.app=e,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return qe()?ze().then(()=>!0).catch(()=>!1):!1}async read(){if(await this._canUseIndexedDBPromise){const n=await jn(this.app);return n?.heartbeats?n:{heartbeats:[]}}else return{heartbeats:[]}}async overwrite(e){var n;if(await this._canUseIndexedDBPromise){const s=await this.read();return Ne(this.app,{lastSentHeartbeatDate:(n=e.lastSentHeartbeatDate)!==null&&n!==void 0?n:s.lastSentHeartbeatDate,heartbeats:e.heartbeats})}else return}async add(e){var n;if(await this._canUseIndexedDBPromise){const s=await this.read();return Ne(this.app,{lastSentHeartbeatDate:(n=e.lastSentHeartbeatDate)!==null&&n!==void 0?n:s.lastSentHeartbeatDate,heartbeats:[...s.heartbeats,...e.heartbeats]})}else return}}function Le(t){return Ve(JSON.stringify({version:2,heartbeats:t})).length}function zn(t){if(t.length===0)return-1;let e=0,n=t[0].date;for(let r=1;r<t.length;r++)t[r].date<n&&(n=t[r].date,e=r);return e}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Gn(t){D(new S("platform-logger",e=>new an(e),"PRIVATE")),D(new S("heartbeat",e=>new Vn(e),"PRIVATE")),R(ce,De,t),R(ce,De,"esm2017"),R("fire-js","")}Gn("");var Jn="firebase",Yn="11.10.0";/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */R(Jn,Yn,"app");const et="@firebase/installations",pe="0.6.18";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const tt=1e4,nt=`w:${pe}`,rt="FIS_v2",Qn="https://firebaseinstallations.googleapis.com/v1",Xn=60*60*1e3,Zn="installations",er="Installations";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const tr={"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."},T=new K(Zn,er,tr);function st(t){return t instanceof O&&t.code.includes("request-failed")}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function it({projectId:t}){return`${Qn}/projects/${t}/installations`}function at(t){return{token:t.token,requestStatus:2,expiresIn:rr(t.expiresIn),creationTime:Date.now()}}async function ot(t,e){const r=(await e.json()).error;return T.create("request-failed",{requestName:t,serverCode:r.code,serverMessage:r.message,serverStatus:r.status})}function ct({apiKey:t}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":t})}function nr(t,{refreshToken:e}){const n=ct(t);return n.append("Authorization",sr(e)),n}async function lt(t){const e=await t();return e.status>=500&&e.status<600?t():e}function rr(t){return Number(t.replace("s","000"))}function sr(t){return`${rt} ${t}`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function ir({appConfig:t,heartbeatServiceProvider:e},{fid:n}){const r=it(t),s=ct(t),i=e.getImmediate({optional:!0});if(i){const l=await i.getHeartbeatsHeader();l&&s.append("x-firebase-client",l)}const a={fid:n,authVersion:rt,appId:t.appId,sdkVersion:nt},o={method:"POST",headers:s,body:JSON.stringify(a)},c=await lt(()=>fetch(r,o));if(c.ok){const l=await c.json();return{fid:l.fid||n,registrationStatus:2,refreshToken:l.refreshToken,authToken:at(l.authToken)}}else throw await ot("Create Installation",c)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ut(t){return new Promise(e=>{setTimeout(e,t)})}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ar(t){return btoa(String.fromCharCode(...t)).replace(/\+/g,"-").replace(/\//g,"_")}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const or=/^[cdef][\w-]{21}$/,he="";function cr(){try{const t=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(t),t[0]=112+t[0]%16;const n=lr(t);return or.test(n)?n:he}catch{return he}}function lr(t){return ar(t).substr(0,22)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function W(t){return`${t.appName}!${t.appId}`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ht=new Map;function dt(t,e){const n=W(t);ft(n,e),ur(n,e)}function ft(t,e){const n=ht.get(t);if(n)for(const r of n)r(e)}function ur(t,e){const n=hr();n&&n.postMessage({key:t,fid:e}),dr()}let C=null;function hr(){return!C&&"BroadcastChannel"in self&&(C=new BroadcastChannel("[Firebase] FID Change"),C.onmessage=t=>{ft(t.data.key,t.data.fid)}),C}function dr(){ht.size===0&&C&&(C.close(),C=null)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const fr="firebase-installations-database",pr=1,A="firebase-installations-store";let X=null;function ge(){return X||(X=V(fr,pr,{upgrade:(t,e)=>{switch(e){case 0:t.createObjectStore(A)}}})),X}async function x(t,e){const n=W(t),s=(await ge()).transaction(A,"readwrite"),i=s.objectStore(A),a=await i.get(n);return await i.put(e,n),await s.done,(!a||a.fid!==e.fid)&&dt(t,e.fid),e}async function pt(t){const e=W(t),r=(await ge()).transaction(A,"readwrite");await r.objectStore(A).delete(e),await r.done}async function q(t,e){const n=W(t),s=(await ge()).transaction(A,"readwrite"),i=s.objectStore(A),a=await i.get(n),o=e(a);return o===void 0?await i.delete(n):await i.put(o,n),await s.done,o&&(!a||a.fid!==o.fid)&&dt(t,o.fid),o}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function me(t){let e;const n=await q(t.appConfig,r=>{const s=gr(r),i=mr(t,s);return e=i.registrationPromise,i.installationEntry});return n.fid===he?{installationEntry:await e}:{installationEntry:n,registrationPromise:e}}function gr(t){const e=t||{fid:cr(),registrationStatus:0};return gt(e)}function mr(t,e){if(e.registrationStatus===0){if(!navigator.onLine){const s=Promise.reject(T.create("app-offline"));return{installationEntry:e,registrationPromise:s}}const n={fid:e.fid,registrationStatus:1,registrationTime:Date.now()},r=br(t,n);return{installationEntry:n,registrationPromise:r}}else return e.registrationStatus===1?{installationEntry:e,registrationPromise:wr(t)}:{installationEntry:e}}async function br(t,e){try{const n=await ir(t,e);return x(t.appConfig,n)}catch(n){throw st(n)&&n.customData.serverCode===409?await pt(t.appConfig):await x(t.appConfig,{fid:e.fid,registrationStatus:0}),n}}async function wr(t){let e=await Pe(t.appConfig);for(;e.registrationStatus===1;)await ut(100),e=await Pe(t.appConfig);if(e.registrationStatus===0){const{installationEntry:n,registrationPromise:r}=await me(t);return r||n}return e}function Pe(t){return q(t,e=>{if(!e)throw T.create("installation-not-found");return gt(e)})}function gt(t){return yr(t)?{fid:t.fid,registrationStatus:0}:t}function yr(t){return t.registrationStatus===1&&t.registrationTime+tt<Date.now()}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function _r({appConfig:t,heartbeatServiceProvider:e},n){const r=Ir(t,n),s=nr(t,n),i=e.getImmediate({optional:!0});if(i){const l=await i.getHeartbeatsHeader();l&&s.append("x-firebase-client",l)}const a={installation:{sdkVersion:nt,appId:t.appId}},o={method:"POST",headers:s,body:JSON.stringify(a)},c=await lt(()=>fetch(r,o));if(c.ok){const l=await c.json();return at(l)}else throw await ot("Generate Auth Token",c)}function Ir(t,{fid:e}){return`${it(t)}/${e}/authTokens:generate`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function be(t,e=!1){let n;const r=await q(t.appConfig,i=>{if(!mt(i))throw T.create("not-registered");const a=i.authToken;if(!e&&Cr(a))return i;if(a.requestStatus===1)return n=vr(t,e),i;{if(!navigator.onLine)throw T.create("app-offline");const o=Tr(i);return n=Er(t,o),o}});return n?await n:r.authToken}async function vr(t,e){let n=await Be(t.appConfig);for(;n.authToken.requestStatus===1;)await ut(100),n=await Be(t.appConfig);const r=n.authToken;return r.requestStatus===0?be(t,e):r}function Be(t){return q(t,e=>{if(!mt(e))throw T.create("not-registered");const n=e.authToken;return Ar(n)?Object.assign(Object.assign({},e),{authToken:{requestStatus:0}}):e})}async function Er(t,e){try{const n=await _r(t,e),r=Object.assign(Object.assign({},e),{authToken:n});return await x(t.appConfig,r),n}catch(n){if(st(n)&&(n.customData.serverCode===401||n.customData.serverCode===404))await pt(t.appConfig);else{const r=Object.assign(Object.assign({},e),{authToken:{requestStatus:0}});await x(t.appConfig,r)}throw n}}function mt(t){return t!==void 0&&t.registrationStatus===2}function Cr(t){return t.requestStatus===2&&!Sr(t)}function Sr(t){const e=Date.now();return e<t.creationTime||t.creationTime+t.expiresIn<e+Xn}function Tr(t){const e={requestStatus:1,requestTime:Date.now()};return Object.assign(Object.assign({},t),{authToken:e})}function Ar(t){return t.requestStatus===1&&t.requestTime+tt<Date.now()}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function kr(t){const e=t,{installationEntry:n,registrationPromise:r}=await me(e);return r?r.catch(console.error):be(e).catch(console.error),n.fid}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Rr(t,e=!1){const n=t;return await Dr(n),(await be(n,e)).token}async function Dr(t){const{registrationPromise:e}=await me(t);e&&await e}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Or(t){if(!t||!t.options)throw Z("App Configuration");if(!t.name)throw Z("App Name");const e=["projectId","apiKey","appId"];for(const n of e)if(!t.options[n])throw Z(n);return{appName:t.name,projectId:t.options.projectId,apiKey:t.options.apiKey,appId:t.options.appId}}function Z(t){return T.create("missing-app-config-values",{valueName:t})}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const bt="installations",Nr="installations-internal",Mr=t=>{const e=t.getProvider("app").getImmediate(),n=Or(e),r=fe(e,"heartbeat");return{app:e,appConfig:n,heartbeatServiceProvider:r,_delete:()=>Promise.resolve()}},Lr=t=>{const e=t.getProvider("app").getImmediate(),n=fe(e,bt).getImmediate();return{getId:()=>kr(n),getToken:s=>Rr(n,s)}};function Pr(){D(new S(bt,Mr,"PUBLIC")),D(new S(Nr,Lr,"PRIVATE"))}Pr();R(et,pe);R(et,pe,"esm2017");/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const wt="BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4",Br="https://fcmregistrations.googleapis.com/v1",yt="FCM_MSG",$r="google.c.a.c_id",Ur=3,xr=1;var F;(function(t){t[t.DATA_MESSAGE=1]="DATA_MESSAGE",t[t.DISPLAY_NOTIFICATION=3]="DISPLAY_NOTIFICATION"})(F||(F={}));/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
 * in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 */var j;(function(t){t.PUSH_RECEIVED="push-received",t.NOTIFICATION_CLICKED="notification-clicked"})(j||(j={}));/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function g(t){const e=new Uint8Array(t);return btoa(String.fromCharCode(...e)).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function Fr(t){const e="=".repeat((4-t.length%4)%4),n=(t+e).replace(/\-/g,"+").replace(/_/g,"/"),r=atob(n),s=new Uint8Array(r.length);for(let i=0;i<r.length;++i)s[i]=r.charCodeAt(i);return s}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ee="fcm_token_details_db",jr=5,$e="fcm_token_object_Store";async function Hr(t){if("databases"in indexedDB&&!(await indexedDB.databases()).map(i=>i.name).includes(ee))return null;let e=null;return(await V(ee,jr,{upgrade:async(r,s,i,a)=>{var o;if(s<2||!r.objectStoreNames.contains($e))return;const c=a.objectStore($e),l=await c.index("fcmSenderId").get(t);if(await c.clear(),!!l){if(s===2){const u=l;if(!u.auth||!u.p256dh||!u.endpoint)return;e={token:u.fcmToken,createTime:(o=u.createTime)!==null&&o!==void 0?o:Date.now(),subscriptionOptions:{auth:u.auth,p256dh:u.p256dh,endpoint:u.endpoint,swScope:u.swScope,vapidKey:typeof u.vapidKey=="string"?u.vapidKey:g(u.vapidKey)}}}else if(s===3){const u=l;e={token:u.fcmToken,createTime:u.createTime,subscriptionOptions:{auth:g(u.auth),p256dh:g(u.p256dh),endpoint:u.endpoint,swScope:u.swScope,vapidKey:g(u.vapidKey)}}}else if(s===4){const u=l;e={token:u.fcmToken,createTime:u.createTime,subscriptionOptions:{auth:g(u.auth),p256dh:g(u.p256dh),endpoint:u.endpoint,swScope:u.swScope,vapidKey:g(u.vapidKey)}}}}}})).close(),await J(ee),await J("fcm_vapid_details_db"),await J("undefined"),Kr(e)?e:null}function Kr(t){if(!t||!t.subscriptionOptions)return!1;const{subscriptionOptions:e}=t;return typeof t.createTime=="number"&&t.createTime>0&&typeof t.token=="string"&&t.token.length>0&&typeof e.auth=="string"&&e.auth.length>0&&typeof e.p256dh=="string"&&e.p256dh.length>0&&typeof e.endpoint=="string"&&e.endpoint.length>0&&typeof e.swScope=="string"&&e.swScope.length>0&&typeof e.vapidKey=="string"&&e.vapidKey.length>0}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Vr="firebase-messaging-database",Wr=1,k="firebase-messaging-store";let te=null;function we(){return te||(te=V(Vr,Wr,{upgrade:(t,e)=>{switch(e){case 0:t.createObjectStore(k)}}})),te}async function ye(t){const e=Ie(t),r=await(await we()).transaction(k).objectStore(k).get(e);if(r)return r;{const s=await Hr(t.appConfig.senderId);if(s)return await _e(t,s),s}}async function _e(t,e){const n=Ie(t),s=(await we()).transaction(k,"readwrite");return await s.objectStore(k).put(e,n),await s.done,e}async function qr(t){const e=Ie(t),r=(await we()).transaction(k,"readwrite");await r.objectStore(k).delete(e),await r.done}function Ie({appConfig:t}){return t.appId}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const zr={"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"only-available-in-window":"This method is available in a Window context.","only-available-in-sw":"This method is available in a service worker context.","permission-default":"The notification permission was not granted and dismissed instead.","permission-blocked":"The notification permission was not granted and blocked instead.","unsupported-browser":"This browser doesn't support the API's required to use the Firebase SDK.","indexed-db-unsupported":"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)","failed-service-worker-registration":"We are unable to register the default service worker. {$browserErrorMessage}","token-subscribe-failed":"A problem occurred while subscribing the user to FCM: {$errorInfo}","token-subscribe-no-token":"FCM returned no token when subscribing the user to push.","token-unsubscribe-failed":"A problem occurred while unsubscribing the user from FCM: {$errorInfo}","token-update-failed":"A problem occurred while updating the user from FCM: {$errorInfo}","token-update-no-token":"FCM returned no token when updating the user to push.","use-sw-after-get-token":"The useServiceWorker() method may only be called once and must be called before calling getToken() to ensure your service worker is used.","invalid-sw-registration":"The input to useServiceWorker() must be a ServiceWorkerRegistration.","invalid-bg-handler":"The input to setBackgroundMessageHandler() must be a function.","invalid-vapid-key":"The public VAPID key must be a string.","use-vapid-key-after-get-token":"The usePublicVapidKey() method may only be called once and must be called before calling getToken() to ensure your VAPID key is used."},p=new K("messaging","Messaging",zr);/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Gr(t,e){const n=await Ee(t),r=It(e),s={method:"POST",headers:n,body:JSON.stringify(r)};let i;try{i=await(await fetch(ve(t.appConfig),s)).json()}catch(a){throw p.create("token-subscribe-failed",{errorInfo:a?.toString()})}if(i.error){const a=i.error.message;throw p.create("token-subscribe-failed",{errorInfo:a})}if(!i.token)throw p.create("token-subscribe-no-token");return i.token}async function Jr(t,e){const n=await Ee(t),r=It(e.subscriptionOptions),s={method:"PATCH",headers:n,body:JSON.stringify(r)};let i;try{i=await(await fetch(`${ve(t.appConfig)}/${e.token}`,s)).json()}catch(a){throw p.create("token-update-failed",{errorInfo:a?.toString()})}if(i.error){const a=i.error.message;throw p.create("token-update-failed",{errorInfo:a})}if(!i.token)throw p.create("token-update-no-token");return i.token}async function _t(t,e){const r={method:"DELETE",headers:await Ee(t)};try{const i=await(await fetch(`${ve(t.appConfig)}/${e}`,r)).json();if(i.error){const a=i.error.message;throw p.create("token-unsubscribe-failed",{errorInfo:a})}}catch(s){throw p.create("token-unsubscribe-failed",{errorInfo:s?.toString()})}}function ve({projectId:t}){return`${Br}/projects/${t}/registrations`}async function Ee({appConfig:t,installations:e}){const n=await e.getToken();return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":t.apiKey,"x-goog-firebase-installations-auth":`FIS ${n}`})}function It({p256dh:t,auth:e,endpoint:n,vapidKey:r}){const s={web:{endpoint:n,auth:e,p256dh:t}};return r!==wt&&(s.web.applicationPubKey=r),s}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Yr=7*24*60*60*1e3;async function Qr(t){const e=await Zr(t.swRegistration,t.vapidKey),n={vapidKey:t.vapidKey,swScope:t.swRegistration.scope,endpoint:e.endpoint,auth:g(e.getKey("auth")),p256dh:g(e.getKey("p256dh"))},r=await ye(t.firebaseDependencies);if(r){if(es(r.subscriptionOptions,n))return Date.now()>=r.createTime+Yr?Xr(t,{token:r.token,createTime:Date.now(),subscriptionOptions:n}):r.token;try{await _t(t.firebaseDependencies,r.token)}catch(s){console.warn(s)}return xe(t.firebaseDependencies,n)}else return xe(t.firebaseDependencies,n)}async function Ue(t){const e=await ye(t.firebaseDependencies);e&&(await _t(t.firebaseDependencies,e.token),await qr(t.firebaseDependencies));const n=await t.swRegistration.pushManager.getSubscription();return n?n.unsubscribe():!0}async function Xr(t,e){try{const n=await Jr(t.firebaseDependencies,e),r=Object.assign(Object.assign({},e),{token:n,createTime:Date.now()});return await _e(t.firebaseDependencies,r),n}catch(n){throw n}}async function xe(t,e){const r={token:await Gr(t,e),createTime:Date.now(),subscriptionOptions:e};return await _e(t,r),r.token}async function Zr(t,e){const n=await t.pushManager.getSubscription();return n||t.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:Fr(e)})}function es(t,e){const n=e.vapidKey===t.vapidKey,r=e.endpoint===t.endpoint,s=e.auth===t.auth,i=e.p256dh===t.p256dh;return n&&r&&s&&i}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ts(t){const e={from:t.from,collapseKey:t.collapse_key,messageId:t.fcmMessageId};return ns(e,t),rs(e,t),ss(e,t),e}function ns(t,e){if(!e.notification)return;t.notification={};const n=e.notification.title;n&&(t.notification.title=n);const r=e.notification.body;r&&(t.notification.body=r);const s=e.notification.image;s&&(t.notification.image=s);const i=e.notification.icon;i&&(t.notification.icon=i)}function rs(t,e){e.data&&(t.data=e.data)}function ss(t,e){var n,r,s,i,a;if(!e.fcmOptions&&!(!((n=e.notification)===null||n===void 0)&&n.click_action))return;t.fcmOptions={};const o=(s=(r=e.fcmOptions)===null||r===void 0?void 0:r.link)!==null&&s!==void 0?s:(i=e.notification)===null||i===void 0?void 0:i.click_action;o&&(t.fcmOptions.link=o);const c=(a=e.fcmOptions)===null||a===void 0?void 0:a.analytics_label;c&&(t.fcmOptions.analyticsLabel=c)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function is(t){return typeof t=="object"&&!!t&&$r in t}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function as(t){return new Promise(e=>{setTimeout(e,t)})}async function os(t,e){const n=cs(e,await t.firebaseDependencies.installations.getId());ls(t,n,e.productId)}function cs(t,e){var n,r;const s={};return t.from&&(s.project_number=t.from),t.fcmMessageId&&(s.message_id=t.fcmMessageId),s.instance_id=e,t.notification?s.message_type=F.DISPLAY_NOTIFICATION.toString():s.message_type=F.DATA_MESSAGE.toString(),s.sdk_platform=Ur.toString(),s.package_name=self.origin.replace(/(^\w+:|^)\/\//,""),t.collapse_key&&(s.collapse_key=t.collapse_key),s.event=xr.toString(),!((n=t.fcmOptions)===null||n===void 0)&&n.analytics_label&&(s.analytics_label=(r=t.fcmOptions)===null||r===void 0?void 0:r.analytics_label),s}function ls(t,e,n){const r={};r.event_time_ms=Math.floor(Date.now()).toString(),r.source_extension_json_proto3=JSON.stringify({messaging_client_event:e}),n&&(r.compliance_data=us(n)),t.logEvents.push(r)}function us(t){return{privacy_context:{prequest:{origin_associated_product_id:t}}}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function hs(t,e){var n,r;const{newSubscription:s}=t;if(!s){await Ue(e);return}const i=await ye(e.firebaseDependencies);await Ue(e),e.vapidKey=(r=(n=i?.subscriptionOptions)===null||n===void 0?void 0:n.vapidKey)!==null&&r!==void 0?r:wt,await Qr(e)}async function ds(t,e){const n=gs(t);if(!n)return;e.deliveryMetricsExportedToBigQueryEnabled&&await os(e,n);const r=await vt();if(bs(r))return ws(r,n);if(n.notification&&await ys(ps(n)),!!e&&e.onBackgroundMessageHandler){const s=ts(n);typeof e.onBackgroundMessageHandler=="function"?await e.onBackgroundMessageHandler(s):e.onBackgroundMessageHandler.next(s)}}async function fs(t){var e,n;const r=(n=(e=t.notification)===null||e===void 0?void 0:e.data)===null||n===void 0?void 0:n[yt];if(r){if(t.action)return}else return;t.stopImmediatePropagation(),t.notification.close();const s=_s(r);if(!s)return;const i=new URL(s,self.location.href),a=new URL(self.location.origin);if(i.host!==a.host)return;let o=await ms(i);if(o?o=await o.focus():(o=await self.clients.openWindow(s),await as(3e3)),!!o)return r.messageType=j.NOTIFICATION_CLICKED,r.isFirebaseMessaging=!0,o.postMessage(r)}function ps(t){const e=Object.assign({},t.notification);return e.data={[yt]:t},e}function gs({data:t}){if(!t)return null;try{return t.json()}catch{return null}}async function ms(t){const e=await vt();for(const n of e){const r=new URL(n.url,self.location.href);if(t.host===r.host)return n}return null}function bs(t){return t.some(e=>e.visibilityState==="visible"&&!e.url.startsWith("chrome-extension://"))}function ws(t,e){e.isFirebaseMessaging=!0,e.messageType=j.PUSH_RECEIVED;for(const n of t)n.postMessage(e)}function vt(){return self.clients.matchAll({type:"window",includeUncontrolled:!0})}function ys(t){var e;const{actions:n}=t,{maxActions:r}=Notification;return n&&r&&n.length>r&&console.warn(`This browser only supports ${r} actions. The remaining actions will not be displayed.`),self.registration.showNotification((e=t.title)!==null&&e!==void 0?e:"",t)}function _s(t){var e,n,r;const s=(n=(e=t.fcmOptions)===null||e===void 0?void 0:e.link)!==null&&n!==void 0?n:(r=t.notification)===null||r===void 0?void 0:r.click_action;return s||(is(t.data)?self.location.origin:null)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Is(t){if(!t||!t.options)throw ne("App Configuration Object");if(!t.name)throw ne("App Name");const e=["projectId","apiKey","appId","messagingSenderId"],{options:n}=t;for(const r of e)if(!n[r])throw ne(r);return{appName:t.name,projectId:n.projectId,apiKey:n.apiKey,appId:n.appId,senderId:n.messagingSenderId}}function ne(t){return p.create("missing-app-config-values",{valueName:t})}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class vs{constructor(e,n,r){this.deliveryMetricsExportedToBigQueryEnabled=!1,this.onBackgroundMessageHandler=null,this.onMessageHandler=null,this.logEvents=[],this.isLogServiceStarted=!1;const s=Is(e);this.firebaseDependencies={app:e,appConfig:s,installations:n,analyticsProvider:r}}_delete(){return Promise.resolve()}}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Es=t=>{const e=new vs(t.getProvider("app").getImmediate(),t.getProvider("installations-internal").getImmediate(),t.getProvider("analytics-internal"));return self.addEventListener("push",n=>{n.waitUntil(ds(n,e))}),self.addEventListener("pushsubscriptionchange",n=>{n.waitUntil(hs(n,e))}),self.addEventListener("notificationclick",n=>{n.waitUntil(fs(n))}),e};function Cs(){D(new S("messaging-sw",Es,"PUBLIC"))}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Ss(){return qe()&&await ze()&&"PushManager"in self&&"Notification"in self&&ServiceWorkerRegistration.prototype.hasOwnProperty("showNotification")&&PushSubscription.prototype.hasOwnProperty("getKey")}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Ts(t,e){if(self.document!==void 0)throw p.create("only-available-in-sw");return t.onBackgroundMessageHandler=e,()=>{t.onBackgroundMessageHandler=null}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function As(t=Un()){return Ss().then(e=>{if(!e)throw p.create("unsupported-browser")},e=>{throw p.create("indexed-db-unsupported")}),fe(Ge(t),"messaging-sw").getImmediate()}function ks(t,e){return t=Ge(t),Ts(t,e)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */Cs();try{self["workbox:core:7.2.0"]&&_()}catch{}const Rs=(t,...e)=>{let n=t;return e.length>0&&(n+=` :: ${JSON.stringify(e)}`),n},Ds=Rs;class f extends Error{constructor(e,n){const r=Ds(e,n);super(r),this.name=e,this.details=n}}const Os=new Set,m={googleAnalytics:"googleAnalytics",precache:"precache-v2",prefix:"workbox",runtime:"runtime",suffix:typeof registration<"u"?registration.scope:""},re=t=>[m.prefix,t,m.suffix].filter(e=>e&&e.length>0).join("-"),Ns=t=>{for(const e of Object.keys(m))t(e)},Ce={updateDetails:t=>{Ns(e=>{typeof t[e]=="string"&&(m[e]=t[e])})},getGoogleAnalyticsName:t=>t||re(m.googleAnalytics),getPrecacheName:t=>t||re(m.precache),getPrefix:()=>m.prefix,getRuntimeName:t=>t||re(m.runtime),getSuffix:()=>m.suffix};function Fe(t,e){const n=new URL(t);for(const r of e)n.searchParams.delete(r);return n.href}async function Ms(t,e,n,r){const s=Fe(e.url,n);if(e.url===s)return t.match(e,r);const i=Object.assign(Object.assign({},r),{ignoreSearch:!0}),a=await t.keys(e,i);for(const o of a){const c=Fe(o.url,n);if(s===c)return t.match(o,r)}}let N;function Ls(){if(N===void 0){const t=new Response("");if("body"in t)try{new Response(t.body),N=!0}catch{N=!1}N=!1}return N}class Ps{constructor(){this.promise=new Promise((e,n)=>{this.resolve=e,this.reject=n})}}async function Bs(){for(const t of Os)await t()}const $s=t=>new URL(String(t),location.href).href.replace(new RegExp(`^${location.origin}`),"");function Us(t){return new Promise(e=>setTimeout(e,t))}function je(t,e){const n=e();return t.waitUntil(n),n}async function xs(t,e){let n=null;if(t.url&&(n=new URL(t.url).origin),n!==self.location.origin)throw new f("cross-origin-copy-response",{origin:n});const r=t.clone(),i={headers:new Headers(r.headers),status:r.status,statusText:r.statusText},a=Ls()?r.body:await r.blob();return new Response(a,i)}function Fs(){self.addEventListener("activate",()=>self.clients.claim())}try{self["workbox:precaching:7.2.0"]&&_()}catch{}const js="__WB_REVISION__";function Hs(t){if(!t)throw new f("add-to-cache-list-unexpected-type",{entry:t});if(typeof t=="string"){const i=new URL(t,location.href);return{cacheKey:i.href,url:i.href}}const{revision:e,url:n}=t;if(!n)throw new f("add-to-cache-list-unexpected-type",{entry:t});if(!e){const i=new URL(n,location.href);return{cacheKey:i.href,url:i.href}}const r=new URL(n,location.href),s=new URL(n,location.href);return r.searchParams.set(js,e),{cacheKey:r.href,url:s.href}}class Ks{constructor(){this.updatedURLs=[],this.notUpdatedURLs=[],this.handlerWillStart=async({request:e,state:n})=>{n&&(n.originalRequest=e)},this.cachedResponseWillBeUsed=async({event:e,state:n,cachedResponse:r})=>{if(e.type==="install"&&n&&n.originalRequest&&n.originalRequest instanceof Request){const s=n.originalRequest.url;r?this.notUpdatedURLs.push(s):this.updatedURLs.push(s)}return r}}}class Vs{constructor({precacheController:e}){this.cacheKeyWillBeUsed=async({request:n,params:r})=>{const s=r?.cacheKey||this._precacheController.getCacheKeyForURL(n.url);return s?new Request(s,{headers:n.headers}):n},this._precacheController=e}}try{self["workbox:strategies:7.2.0"]&&_()}catch{}function $(t){return typeof t=="string"?new Request(t):t}class Ws{constructor(e,n){this._cacheKeys={},Object.assign(this,n),this.event=n.event,this._strategy=e,this._handlerDeferred=new Ps,this._extendLifetimePromises=[],this._plugins=[...e.plugins],this._pluginStateMap=new Map;for(const r of this._plugins)this._pluginStateMap.set(r,{});this.event.waitUntil(this._handlerDeferred.promise)}async fetch(e){const{event:n}=this;let r=$(e);if(r.mode==="navigate"&&n instanceof FetchEvent&&n.preloadResponse){const a=await n.preloadResponse;if(a)return a}const s=this.hasCallback("fetchDidFail")?r.clone():null;try{for(const a of this.iterateCallbacks("requestWillFetch"))r=await a({request:r.clone(),event:n})}catch(a){if(a instanceof Error)throw new f("plugin-error-request-will-fetch",{thrownErrorMessage:a.message})}const i=r.clone();try{let a;a=await fetch(r,r.mode==="navigate"?void 0:this._strategy.fetchOptions);for(const o of this.iterateCallbacks("fetchDidSucceed"))a=await o({event:n,request:i,response:a});return a}catch(a){throw s&&await this.runCallbacks("fetchDidFail",{error:a,event:n,originalRequest:s.clone(),request:i.clone()}),a}}async fetchAndCachePut(e){const n=await this.fetch(e),r=n.clone();return this.waitUntil(this.cachePut(e,r)),n}async cacheMatch(e){const n=$(e);let r;const{cacheName:s,matchOptions:i}=this._strategy,a=await this.getCacheKey(n,"read"),o=Object.assign(Object.assign({},i),{cacheName:s});r=await caches.match(a,o);for(const c of this.iterateCallbacks("cachedResponseWillBeUsed"))r=await c({cacheName:s,matchOptions:i,cachedResponse:r,request:a,event:this.event})||void 0;return r}async cachePut(e,n){const r=$(e);await Us(0);const s=await this.getCacheKey(r,"write");if(!n)throw new f("cache-put-with-no-response",{url:$s(s.url)});const i=await this._ensureResponseSafeToCache(n);if(!i)return!1;const{cacheName:a,matchOptions:o}=this._strategy,c=await self.caches.open(a),l=this.hasCallback("cacheDidUpdate"),u=l?await Ms(c,s.clone(),["__WB_REVISION__"],o):null;try{await c.put(s,l?i.clone():i)}catch(d){if(d instanceof Error)throw d.name==="QuotaExceededError"&&await Bs(),d}for(const d of this.iterateCallbacks("cacheDidUpdate"))await d({cacheName:a,oldResponse:u,newResponse:i.clone(),request:s,event:this.event});return!0}async getCacheKey(e,n){const r=`${e.url} | ${n}`;if(!this._cacheKeys[r]){let s=e;for(const i of this.iterateCallbacks("cacheKeyWillBeUsed"))s=$(await i({mode:n,request:s,event:this.event,params:this.params}));this._cacheKeys[r]=s}return this._cacheKeys[r]}hasCallback(e){for(const n of this._strategy.plugins)if(e in n)return!0;return!1}async runCallbacks(e,n){for(const r of this.iterateCallbacks(e))await r(n)}*iterateCallbacks(e){for(const n of this._strategy.plugins)if(typeof n[e]=="function"){const r=this._pluginStateMap.get(n);yield i=>{const a=Object.assign(Object.assign({},i),{state:r});return n[e](a)}}}waitUntil(e){return this._extendLifetimePromises.push(e),e}async doneWaiting(){let e;for(;e=this._extendLifetimePromises.shift();)await e}destroy(){this._handlerDeferred.resolve(null)}async _ensureResponseSafeToCache(e){let n=e,r=!1;for(const s of this.iterateCallbacks("cacheWillUpdate"))if(n=await s({request:this.request,response:n,event:this.event})||void 0,r=!0,!n)break;return r||n&&n.status!==200&&(n=void 0),n}}class qs{constructor(e={}){this.cacheName=Ce.getRuntimeName(e.cacheName),this.plugins=e.plugins||[],this.fetchOptions=e.fetchOptions,this.matchOptions=e.matchOptions}handle(e){const[n]=this.handleAll(e);return n}handleAll(e){e instanceof FetchEvent&&(e={event:e,request:e.request});const n=e.event,r=typeof e.request=="string"?new Request(e.request):e.request,s="params"in e?e.params:void 0,i=new Ws(this,{event:n,request:r,params:s}),a=this._getResponse(i,r,n),o=this._awaitComplete(a,i,r,n);return[a,o]}async _getResponse(e,n,r){await e.runCallbacks("handlerWillStart",{event:r,request:n});let s;try{if(s=await this._handle(n,e),!s||s.type==="error")throw new f("no-response",{url:n.url})}catch(i){if(i instanceof Error){for(const a of e.iterateCallbacks("handlerDidError"))if(s=await a({error:i,event:r,request:n}),s)break}if(!s)throw i}for(const i of e.iterateCallbacks("handlerWillRespond"))s=await i({event:r,request:n,response:s});return s}async _awaitComplete(e,n,r,s){let i,a;try{i=await e}catch{}try{await n.runCallbacks("handlerDidRespond",{event:s,request:r,response:i}),await n.doneWaiting()}catch(o){o instanceof Error&&(a=o)}if(await n.runCallbacks("handlerDidComplete",{event:s,request:r,response:i,error:a}),n.destroy(),a)throw a}}class y extends qs{constructor(e={}){e.cacheName=Ce.getPrecacheName(e.cacheName),super(e),this._fallbackToNetwork=e.fallbackToNetwork!==!1,this.plugins.push(y.copyRedirectedCacheableResponsesPlugin)}async _handle(e,n){const r=await n.cacheMatch(e);return r||(n.event&&n.event.type==="install"?await this._handleInstall(e,n):await this._handleFetch(e,n))}async _handleFetch(e,n){let r;const s=n.params||{};if(this._fallbackToNetwork){const i=s.integrity,a=e.integrity,o=!a||a===i;r=await n.fetch(new Request(e,{integrity:e.mode!=="no-cors"?a||i:void 0})),i&&o&&e.mode!=="no-cors"&&(this._useDefaultCacheabilityPluginIfNeeded(),await n.cachePut(e,r.clone()))}else throw new f("missing-precache-entry",{cacheName:this.cacheName,url:e.url});return r}async _handleInstall(e,n){this._useDefaultCacheabilityPluginIfNeeded();const r=await n.fetch(e);if(!await n.cachePut(e,r.clone()))throw new f("bad-precaching-response",{url:e.url,status:r.status});return r}_useDefaultCacheabilityPluginIfNeeded(){let e=null,n=0;for(const[r,s]of this.plugins.entries())s!==y.copyRedirectedCacheableResponsesPlugin&&(s===y.defaultPrecacheCacheabilityPlugin&&(e=r),s.cacheWillUpdate&&n++);n===0?this.plugins.push(y.defaultPrecacheCacheabilityPlugin):n>1&&e!==null&&this.plugins.splice(e,1)}}y.defaultPrecacheCacheabilityPlugin={async cacheWillUpdate({response:t}){return!t||t.status>=400?null:t}};y.copyRedirectedCacheableResponsesPlugin={async cacheWillUpdate({response:t}){return t.redirected?await xs(t):t}};class zs{constructor({cacheName:e,plugins:n=[],fallbackToNetwork:r=!0}={}){this._urlsToCacheKeys=new Map,this._urlsToCacheModes=new Map,this._cacheKeysToIntegrities=new Map,this._strategy=new y({cacheName:Ce.getPrecacheName(e),plugins:[...n,new Vs({precacheController:this})],fallbackToNetwork:r}),this.install=this.install.bind(this),this.activate=this.activate.bind(this)}get strategy(){return this._strategy}precache(e){this.addToCacheList(e),this._installAndActiveListenersAdded||(self.addEventListener("install",this.install),self.addEventListener("activate",this.activate),this._installAndActiveListenersAdded=!0)}addToCacheList(e){const n=[];for(const r of e){typeof r=="string"?n.push(r):r&&r.revision===void 0&&n.push(r.url);const{cacheKey:s,url:i}=Hs(r),a=typeof r!="string"&&r.revision?"reload":"default";if(this._urlsToCacheKeys.has(i)&&this._urlsToCacheKeys.get(i)!==s)throw new f("add-to-cache-list-conflicting-entries",{firstEntry:this._urlsToCacheKeys.get(i),secondEntry:s});if(typeof r!="string"&&r.integrity){if(this._cacheKeysToIntegrities.has(s)&&this._cacheKeysToIntegrities.get(s)!==r.integrity)throw new f("add-to-cache-list-conflicting-integrities",{url:i});this._cacheKeysToIntegrities.set(s,r.integrity)}if(this._urlsToCacheKeys.set(i,s),this._urlsToCacheModes.set(i,a),n.length>0){const o=`Workbox is precaching URLs without revision info: ${n.join(", ")}
This is generally NOT safe. Learn more at https://bit.ly/wb-precache`;console.warn(o)}}}install(e){return je(e,async()=>{const n=new Ks;this.strategy.plugins.push(n);for(const[i,a]of this._urlsToCacheKeys){const o=this._cacheKeysToIntegrities.get(a),c=this._urlsToCacheModes.get(i),l=new Request(i,{integrity:o,cache:c,credentials:"same-origin"});await Promise.all(this.strategy.handleAll({params:{cacheKey:a},request:l,event:e}))}const{updatedURLs:r,notUpdatedURLs:s}=n;return{updatedURLs:r,notUpdatedURLs:s}})}activate(e){return je(e,async()=>{const n=await self.caches.open(this.strategy.cacheName),r=await n.keys(),s=new Set(this._urlsToCacheKeys.values()),i=[];for(const a of r)s.has(a.url)||(await n.delete(a),i.push(a.url));return{deletedURLs:i}})}getURLsToCacheKeys(){return this._urlsToCacheKeys}getCachedURLs(){return[...this._urlsToCacheKeys.keys()]}getCacheKeyForURL(e){const n=new URL(e,location.href);return this._urlsToCacheKeys.get(n.href)}getIntegrityForCacheKey(e){return this._cacheKeysToIntegrities.get(e)}async matchPrecache(e){const n=e instanceof Request?e.url:e,r=this.getCacheKeyForURL(n);if(r)return(await self.caches.open(this.strategy.cacheName)).match(r)}createHandlerBoundToURL(e){const n=this.getCacheKeyForURL(e);if(!n)throw new f("non-precached-url",{url:e});return r=>(r.request=new Request(e),r.params=Object.assign({cacheKey:n},r.params),this.strategy.handle(r))}}let se;const Et=()=>(se||(se=new zs),se);try{self["workbox:routing:7.2.0"]&&_()}catch{}const Ct="GET",H=t=>t&&typeof t=="object"?t:{handle:t};class L{constructor(e,n,r=Ct){this.handler=H(n),this.match=e,this.method=r}setCatchHandler(e){this.catchHandler=H(e)}}class Gs extends L{constructor(e,n,r){const s=({url:i})=>{const a=e.exec(i.href);if(a&&!(i.origin!==location.origin&&a.index!==0))return a.slice(1)};super(s,n,r)}}class Js{constructor(){this._routes=new Map,this._defaultHandlerMap=new Map}get routes(){return this._routes}addFetchListener(){self.addEventListener("fetch",e=>{const{request:n}=e,r=this.handleRequest({request:n,event:e});r&&e.respondWith(r)})}addCacheListener(){self.addEventListener("message",e=>{if(e.data&&e.data.type==="CACHE_URLS"){const{payload:n}=e.data,r=Promise.all(n.urlsToCache.map(s=>{typeof s=="string"&&(s=[s]);const i=new Request(...s);return this.handleRequest({request:i,event:e})}));e.waitUntil(r),e.ports&&e.ports[0]&&r.then(()=>e.ports[0].postMessage(!0))}})}handleRequest({request:e,event:n}){const r=new URL(e.url,location.href);if(!r.protocol.startsWith("http"))return;const s=r.origin===location.origin,{params:i,route:a}=this.findMatchingRoute({event:n,request:e,sameOrigin:s,url:r});let o=a&&a.handler;const c=e.method;if(!o&&this._defaultHandlerMap.has(c)&&(o=this._defaultHandlerMap.get(c)),!o)return;let l;try{l=o.handle({url:r,request:e,event:n,params:i})}catch(d){l=Promise.reject(d)}const u=a&&a.catchHandler;return l instanceof Promise&&(this._catchHandler||u)&&(l=l.catch(async d=>{if(u)try{return await u.handle({url:r,request:e,event:n,params:i})}catch(v){v instanceof Error&&(d=v)}if(this._catchHandler)return this._catchHandler.handle({url:r,request:e,event:n});throw d})),l}findMatchingRoute({url:e,sameOrigin:n,request:r,event:s}){const i=this._routes.get(r.method)||[];for(const a of i){let o;const c=a.match({url:e,sameOrigin:n,request:r,event:s});if(c)return o=c,(Array.isArray(o)&&o.length===0||c.constructor===Object&&Object.keys(c).length===0||typeof c=="boolean")&&(o=void 0),{route:a,params:o}}return{}}setDefaultHandler(e,n=Ct){this._defaultHandlerMap.set(n,H(e))}setCatchHandler(e){this._catchHandler=H(e)}registerRoute(e){this._routes.has(e.method)||this._routes.set(e.method,[]),this._routes.get(e.method).push(e)}unregisterRoute(e){if(!this._routes.has(e.method))throw new f("unregister-route-but-not-found-with-method",{method:e.method});const n=this._routes.get(e.method).indexOf(e);if(n>-1)this._routes.get(e.method).splice(n,1);else throw new f("unregister-route-route-not-registered")}}let M;const Ys=()=>(M||(M=new Js,M.addFetchListener(),M.addCacheListener()),M);function Qs(t,e,n){let r;if(typeof t=="string"){const i=new URL(t,location.href),a=({url:o})=>o.href===i.href;r=new L(a,e,n)}else if(t instanceof RegExp)r=new Gs(t,e,n);else if(typeof t=="function")r=new L(t,e,n);else if(t instanceof L)r=t;else throw new f("unsupported-route-type",{moduleName:"workbox-routing",funcName:"registerRoute",paramName:"capture"});return Ys().registerRoute(r),r}function Xs(t,e=[]){for(const n of[...t.searchParams.keys()])e.some(r=>r.test(n))&&t.searchParams.delete(n);return t}function*Zs(t,{ignoreURLParametersMatching:e=[/^utm_/,/^fbclid$/],directoryIndex:n="index.html",cleanURLs:r=!0,urlManipulation:s}={}){const i=new URL(t,location.href);i.hash="",yield i.href;const a=Xs(i,e);if(yield a.href,n&&a.pathname.endsWith("/")){const o=new URL(a.href);o.pathname+=n,yield o.href}if(r){const o=new URL(a.href);o.pathname+=".html",yield o.href}if(s){const o=s({url:i});for(const c of o)yield c.href}}class ei extends L{constructor(e,n){const r=({request:s})=>{const i=e.getURLsToCacheKeys();for(const a of Zs(s.url,n)){const o=i.get(a);if(o){const c=e.getIntegrityForCacheKey(o);return{cacheKey:o,integrity:c}}}};super(r,e.strategy)}}function ti(t){const e=Et(),n=new ei(e,t);Qs(n)}function ni(t){Et().precache(t)}function ri(t,e){ni(t),ti(e)}ri([{"revision":"400f90d6f5f42eeef7d9df0f1bac6c82","url":"apple-touch-icon.png"},{"revision":null,"url":"assets/Abilities-ChSGUO4O.js"},{"revision":null,"url":"assets/abilities-OclsAqoB.png"},{"revision":null,"url":"assets/admin-CxPP_qbb.png"},{"revision":null,"url":"assets/adventure-Bp-cQoF0.png"},{"revision":null,"url":"assets/AdventurePage-3duGIljm.js"},{"revision":null,"url":"assets/ag-theme-quartz-BKv5Qpmd.css"},{"revision":null,"url":"assets/ag-theme-quartz-BoTMoAbA.js"},{"revision":null,"url":"assets/angry-DYivJtBQ.png"},{"revision":null,"url":"assets/APicon3-CTiyfJlQ.png"},{"revision":null,"url":"assets/arcade-BIkkTRDG.png"},{"revision":null,"url":"assets/Arcade-kIqLUsjv.js"},{"revision":null,"url":"assets/arrow-left-BgvakO9l.js"},{"revision":null,"url":"assets/arrow-uJJzj90d.gif"},{"revision":null,"url":"assets/attack-BLmMBc-T.png"},{"revision":null,"url":"assets/backButton-DvB-fftI.png"},{"revision":null,"url":"assets/backgroundImg-3EK4Ifxm.webp"},{"revision":null,"url":"assets/backgroundImg-BZ7IgI6U.webp"},{"revision":null,"url":"assets/backgroundImg-C8THYgdB.webp"},{"revision":null,"url":"assets/backgroundImg-CaBgDC45.webp"},{"revision":null,"url":"assets/backgroundImg-cxMMHQS6.webp"},{"revision":null,"url":"assets/backgroundImg-CxrQswXt.webp"},{"revision":null,"url":"assets/baka-D-4TckS2.png"},{"revision":null,"url":"assets/Bank-BtdQjlji.js"},{"revision":null,"url":"assets/banknote-7DhR7Ha5.js"},{"revision":null,"url":"assets/berserker-CcNKNwk6.png"},{"revision":null,"url":"assets/blue3-Bkwl1QpL.jpg"},{"revision":null,"url":"assets/blueBars-X0WSUevI.jpg"},{"revision":null,"url":"assets/blueframe-DYlU065E.png"},{"revision":null,"url":"assets/blueSliderFill-CJOtkgQV.png"},{"revision":null,"url":"assets/bodyRegen-CUoc4dCz.png"},{"revision":null,"url":"assets/book-open-CnM1sAIc.js"},{"revision":null,"url":"assets/BountyBoard-d9QHWP7E.js"},{"revision":null,"url":"assets/brain-DCVPzKMe.js"},{"revision":null,"url":"assets/built-Ekk7e7JB.png"},{"revision":null,"url":"assets/bully-BrIGeiHK.png"},{"revision":null,"url":"assets/bunny-DgsWqt8r.png"},{"revision":null,"url":"assets/cafe-Czyfu_EL.webp"},{"revision":null,"url":"assets/calendar-CWGSV0sS.js"},{"revision":null,"url":"assets/Calendar-oB26m64l.js"},{"revision":null,"url":"assets/Callout-BPProctn.js"},{"revision":null,"url":"assets/cartoonPattern-oMTjJ3v0.png"},{"revision":null,"url":"assets/cartoonPatternFull-DWt-CNTS.png"},{"revision":null,"url":"assets/Casino-CEc-J9hB.js"},{"revision":null,"url":"assets/casino-esO0707x.webp"},{"revision":null,"url":"assets/catjump-CPyW3lbX.gif"},{"revision":null,"url":"assets/chaining-DG-sm0UC.png"},{"revision":null,"url":"assets/character-ClxC3w3i.png"},{"revision":null,"url":"assets/Character-OfCW_zI9.js"},{"revision":null,"url":"assets/chat-DISOVqRS.png"},{"revision":null,"url":"assets/chatMsg-CW79yXce.mp3"},{"revision":null,"url":"assets/chevron-up-pgv_lXQk.js"},{"revision":null,"url":"assets/christmas-DEu9Iv_I.png"},{"revision":null,"url":"assets/circle-alert-DJ5_ejTF.js"},{"revision":null,"url":"assets/circle-check-big-CBbNAtqE.js"},{"revision":null,"url":"assets/circle-question-mark-BDAnzkvO.js"},{"revision":null,"url":"assets/circle-x-COFLFM78.js"},{"revision":null,"url":"assets/classroom-CLhUWM0N.webp"},{"revision":null,"url":"assets/classroom1Day-BF35Yh1x.webp"},{"revision":null,"url":"assets/claw-B8W427Tp.png"},{"revision":null,"url":"assets/combatRegen-BiCxXyaJ.png"},{"revision":null,"url":"assets/construction-DabPkmp4.webp"},{"revision":null,"url":"assets/consumables-lUwNcz6B.png"},{"revision":null,"url":"assets/continue-BIFtnR7n.png"},{"revision":null,"url":"assets/Courses-Drpc0lK2.js"},{"revision":null,"url":"assets/coward-Da6VeiGp.png"},{"revision":null,"url":"assets/craft-ByVwmzC4.png"},{"revision":null,"url":"assets/CraftingWorkshopPage-NZuZE8oP.js"},{"revision":null,"url":"assets/cripple-wLssv--F.png"},{"revision":null,"url":"assets/crippled-R8ZmHpKe.png"},{"revision":null,"url":"assets/cry-Xox1O2WK.png"},{"revision":null,"url":"assets/culture-BV_CsUBG.png"},{"revision":null,"url":"assets/cunning-DV04IzD5.png"},{"revision":null,"url":"assets/currency2-Bpjz_OrF.png"},{"revision":null,"url":"assets/dailychest-OOUVtqsz.png"},{"revision":null,"url":"assets/DailyTaskPage-Bs6ReCpU.js"},{"revision":null,"url":"assets/darkBlueButtonBG-CtVbXbmJ.svg"},{"revision":null,"url":"assets/darkBlueButtonBG-DlkDGQfX.png"},{"revision":null,"url":"assets/DataTable-CsG3npGc.js"},{"revision":null,"url":"assets/defaultAvatar-DdtHYP0A.png"},{"revision":null,"url":"assets/defence-MpD812P6.png"},{"revision":null,"url":"assets/deflect--BTIcwGf.png"},{"revision":null,"url":"assets/dexterity-6ahwAW6O.png"},{"revision":null,"url":"assets/dies-DI2d-WMs.gif"},{"revision":null,"url":"assets/differenceInHours-C_eXMFZA.js"},{"revision":null,"url":"assets/disarm-D9y5Gj7F.png"},{"revision":null,"url":"assets/disarmed-PHcQYjWn.png"},{"revision":null,"url":"assets/disclosure-6cPc6BxT.js"},{"revision":null,"url":"assets/Discord-CQ7V2Gv6.js"},{"revision":null,"url":"assets/dumbbell-D3jmHIoc.js"},{"revision":null,"url":"assets/emo8-GiwerJ1C.png"},{"revision":null,"url":"assets/emojiButton-BXHSeT55.png"},{"revision":null,"url":"assets/energetic-ls-tXnMT.png"},{"revision":null,"url":"assets/energyicon-BwINCWoA.png"},{"revision":null,"url":"assets/enhanced-BNSBM5nP.png"},{"revision":null,"url":"assets/enraged-84ZbFSNp.png"},{"revision":null,"url":"assets/escapeArtist-BJ99H1_1.png"},{"revision":null,"url":"assets/events-BkXSD3Ep.png"},{"revision":null,"url":"assets/Events-BprHQRQK.js"},{"revision":null,"url":"assets/exhaust-DCKeUgI1.png"},{"revision":null,"url":"assets/exhausted-Cr1rRUFR.png"},{"revision":null,"url":"assets/expBG-BGDsopHP.png"},{"revision":null,"url":"assets/expicon-BeF7u3m_.png"},{"revision":null,"url":"assets/explore-BuhS9zcc.png"},{"revision":null,"url":"assets/ExplorePage-DSvJ9dGV.js"},{"revision":null,"url":"assets/expSliderBG-CnhHBKjT.png"},{"revision":null,"url":"assets/expSliderFill-BhwbzH6r.png"},{"revision":null,"url":"assets/FacultyList-KA4XOVB4.js"},{"revision":null,"url":"assets/fetch-DHMFazHH.png"},{"revision":null,"url":"assets/flee-R9tikEwx.png"},{"revision":null,"url":"assets/freeMovement-BisXPF0c.png"},{"revision":null,"url":"assets/frenzied-5CbmBTWG.png"},{"revision":null,"url":"assets/FullChat-BNxA5irI.js"},{"revision":null,"url":"assets/gang-CIz4GXEf.png"},{"revision":null,"url":"assets/Gang-DBMi4ove.js"},{"revision":null,"url":"assets/GangLeaderboards-beVIO4uB.js"},{"revision":null,"url":"assets/GangList-CNqaDUqY.js"},{"revision":null,"url":"assets/getJobImage-D75E1RnK.js"},{"revision":null,"url":"assets/getUserItemCount-CIJt4adY.js"},{"revision":null,"url":"assets/grayButtonBG-BU03f1GU.svg"},{"revision":null,"url":"assets/grayButtonBG-qEuiB7EG.png"},{"revision":null,"url":"assets/green1-Dj5gmr5Y.jpg"},{"revision":null,"url":"assets/greenframe-CbRYt0fX.png"},{"revision":null,"url":"assets/greenSliderFill-CYQBp405.png"},{"revision":null,"url":"assets/greenTick-CRl6ZXFn.js"},{"revision":null,"url":"assets/guarding-LiwX2fx2.png"},{"revision":null,"url":"assets/hallway1Day-BuLLK_P1.webp"},{"revision":null,"url":"assets/hammer-CHGTBT4f.js"},{"revision":null,"url":"assets/happy-D53xDMqa.webp"},{"revision":null,"url":"assets/happyopen-BjJ5ngIm.webp"},{"revision":null,"url":"assets/happyopen-BywJr-Fd.webp"},{"revision":null,"url":"assets/happyopen-CRvy9v7Z.webp"},{"revision":null,"url":"assets/happyopenShadow-mtmPWIpd.webp"},{"revision":null,"url":"assets/happyopenSmall-CfbW0upK.webp"},{"revision":null,"url":"assets/happyopenSmall-Cz3BU7F2.webp"},{"revision":null,"url":"assets/happyopenSmall-DbUUICuH.webp"},{"revision":null,"url":"assets/happySmall-C42ob1BY.webp"},{"revision":null,"url":"assets/headbutt-B1_YV36I.png"},{"revision":null,"url":"assets/headmaster_suit_angry-CTLLzgFC.webp"},{"revision":null,"url":"assets/heal-fRmk4vQ3.png"},{"revision":null,"url":"assets/healovertime-edkXbaAD.png"},{"revision":null,"url":"assets/healthyCaster-eX1umIhb.png"},{"revision":null,"url":"assets/heh-txDCJ7q0.png"},{"revision":null,"url":"assets/highguard-9KUfg1bT.png"},{"revision":null,"url":"assets/hii-D7S-nf01.gif"},{"revision":null,"url":"assets/hmm-C5GjdB-J.png"},{"revision":null,"url":"assets/Hospital-ClV-JbZJ.js"},{"revision":null,"url":"assets/hospital-yQ8YbERO.png"},{"revision":null,"url":"assets/HPicon-D4GSC55Z.png"},{"revision":null,"url":"assets/HPicon2-BpgU42R7.png"},{"revision":null,"url":"assets/hyped-BXWhD6Xn.gif"},{"revision":null,"url":"assets/hyperjump-BxOnrsHK.gif"},{"revision":null,"url":"assets/Icon_ImageIcon_Medal_Bronze-MKBcOfF8.png"},{"revision":null,"url":"assets/Icon_ImageIcon_Medal_Gold-T6T-L8Ga.png"},{"revision":null,"url":"assets/Icon_ImageIcon_Medal_Silver-CEIwxd4F.png"},{"revision":null,"url":"assets/Icon_ImageIcon_Medal_Silver-OH_4ykaO.js"},{"revision":null,"url":"assets/Inbox-BUqfBcvq.css"},{"revision":null,"url":"assets/Inbox-KBFS0mQe.js"},{"revision":null,"url":"assets/index-BNv9-hhu.js"},{"revision":null,"url":"assets/index-C27vWFUK.css"},{"revision":null,"url":"assets/index-CYAk2k9S.js"},{"revision":null,"url":"assets/intelligence-BrHC6g6d.png"},{"revision":null,"url":"assets/intervalToDuration-DRMO9bY5.js"},{"revision":null,"url":"assets/Inventory-Ci96Qp3x.css"},{"revision":null,"url":"assets/Inventory-DwUQ8HGb.js"},{"revision":null,"url":"assets/inventory-Dz1d2pHE.png"},{"revision":null,"url":"assets/investor-Dvl8Hu30.png"},{"revision":null,"url":"assets/Jail-CAM06EQ2.js"},{"revision":null,"url":"assets/jail-DSmMbh5p.png"},{"revision":null,"url":"assets/jailbars-BvzW8JvC.png"},{"revision":null,"url":"assets/jobs-BDLp4JM9.webp"},{"revision":null,"url":"assets/jump-Bl2WOmC2.gif"},{"revision":null,"url":"assets/kalm-BZb22g9v.png"},{"revision":null,"url":"assets/kappa-DoK2B1Jz.png"},{"revision":null,"url":"assets/LatestNews--RqqgCN5.js"},{"revision":null,"url":"assets/leaderboard-D6o9iRcx.webp"},{"revision":null,"url":"assets/Leaderboard-DJudHy6G.js"},{"revision":null,"url":"assets/leaderboards-CdqvSwhr.png"},{"revision":null,"url":"assets/learner-BVuwyufs.png"},{"revision":null,"url":"assets/leftarrow-DZyJmGgR.png"},{"revision":null,"url":"assets/legendary-DOZ_Skqf.png"},{"revision":null,"url":"assets/levelBadge-uEikayln.png"},{"revision":null,"url":"assets/levelup-CB5E89to.png"},{"revision":null,"url":"assets/lexend-C4kThqOm.woff2"},{"revision":null,"url":"assets/lifeEssence-BsbKaMCd.png"},{"revision":null,"url":"assets/light-BHrEBvor.png"},{"revision":null,"url":"assets/lmao-Dan7iibn.gif"},{"revision":null,"url":"assets/locked-ly7i4j0n.png"},{"revision":null,"url":"assets/lockedchest-Db25p4X2.png"},{"revision":null,"url":"assets/logoCharacter-CKZKBhoy.webp"},{"revision":null,"url":"assets/logoTextSVG-D4njN0cX.svg"},{"revision":null,"url":"assets/map-pin-BLbZ2JzX.js"},{"revision":null,"url":"assets/Market-fajo-EDF.js"},{"revision":null,"url":"assets/melee-DzjqxZF9.png"},{"revision":null,"url":"assets/meleeDmg-DocnD4s9.png"},{"revision":null,"url":"assets/menu-DMy37JpN.js"},{"revision":null,"url":"assets/messages-D_U0ynyw.png"},{"revision":null,"url":"assets/metabolism-Bb1bhdi5.png"},{"revision":null,"url":"assets/military-Br_mmXDW.png"},{"revision":null,"url":"assets/missingIcon-BZT45_3d.png"},{"revision":null,"url":"assets/Missions-CQh6dYps.js"},{"revision":null,"url":"assets/mitigation-Bcff0MX6.png"},{"revision":null,"url":"assets/mobileNavBG-DVDFipyq.png"},{"revision":null,"url":"assets/mobileNavBGDark-CB0uG0GA.png"},{"revision":null,"url":"assets/mugger-DyLN16EX.png"},{"revision":null,"url":"assets/multitasker-D8QqoWZR.png"},{"revision":null,"url":"assets/NetworkError-CsPzDWii.png"},{"revision":null,"url":"assets/neutral-CKcwP3Gg.webp"},{"revision":null,"url":"assets/neutral-DydpajS9.webp"},{"revision":null,"url":"assets/neutralShadow-CTW1ix-z.webp"},{"revision":null,"url":"assets/neutralShadow-DV9lvAE8.webp"},{"revision":null,"url":"assets/neutralSmall-C7dyofyr.webp"},{"revision":null,"url":"assets/neutralSmall-CiDumcaB.webp"},{"revision":null,"url":"assets/news-CZbTg9Bq.png"},{"revision":null,"url":"assets/no-BAs6ATi3.gif"},{"revision":null,"url":"assets/nobully-hddR5pUr.png"},{"revision":null,"url":"assets/novice-5eh4GiAI.png"},{"revision":null,"url":"assets/npc_kill-V8wRFwoS.png"},{"revision":null,"url":"assets/offhands-D1CAKh_r.png"},{"revision":null,"url":"assets/office-CE9JU_6X.webp"},{"revision":null,"url":"assets/orangeBars-C9x1tbO3.jpg"},{"revision":null,"url":"assets/panik-BipToAHb.png"},{"revision":null,"url":"assets/PartTimeJobListings-CLqfQNXB.js"},{"revision":null,"url":"assets/patchNotes-CWZ9Xpkl.js"},{"revision":null,"url":"assets/PetsPage-Cl2dLaFq.js"},{"revision":null,"url":"assets/pink2-CjLFDBrJ.jpg"},{"revision":null,"url":"assets/poisoned-8J8o479p.png"},{"revision":null,"url":"assets/Polls-Bx2MlljU.css"},{"revision":null,"url":"assets/Polls-W-pYLz2n.js"},{"revision":null,"url":"assets/popcat-N8Tq8q_D.gif"},{"revision":null,"url":"assets/processing-ClkX9LpC.png"},{"revision":null,"url":"assets/PropertyPage-CCypUl1k.js"},{"revision":null,"url":"assets/purple2-DH370tFu.jpg"},{"revision":null,"url":"assets/pvp_kill-sG7FJmrZ.png"},{"revision":null,"url":"assets/quiver-CEWXcocX.png"},{"revision":null,"url":"assets/rage-CxQ1PUH2.png"},{"revision":null,"url":"assets/ramen-BWFMw-vw.webp"},{"revision":null,"url":"assets/ranged-DeiLteUu.png"},{"revision":null,"url":"assets/ranger-T0q4gbQT.png"},{"revision":null,"url":"assets/rarityColours-DbAhnmJg.js"},{"revision":null,"url":"assets/rawMaterials-BGJJK4mz.png"},{"revision":null,"url":"assets/recovery-CVUd61zL.png"},{"revision":null,"url":"assets/Referrals-CcJL1ygf.js"},{"revision":null,"url":"assets/rejuvenation-Bho8_TBt.png"},{"revision":null,"url":"assets/reload-CcVyUsaa.png"},{"revision":null,"url":"assets/respect-C47rUSm7.png"},{"revision":null,"url":"assets/ribbonPurple-Cto_nvlv.png"},{"revision":null,"url":"assets/ribbonRed-DuSpEbKG.png"},{"revision":null,"url":"assets/rightarrow-DmLB-4mF.png"},{"revision":null,"url":"assets/rigidity-D0891Q6Y.png"},{"revision":null,"url":"assets/Rooftop-DnUYYkxH.js"},{"revision":null,"url":"assets/sad-DYUjJwXW.png"},{"revision":null,"url":"assets/school-CYIItOoz.png"},{"revision":null,"url":"assets/School-CyxEO5nN.js"},{"revision":null,"url":"assets/search-Cd4bdjpR.png"},{"revision":null,"url":"assets/secondWind-IOUYxsOR.png"},{"revision":null,"url":"assets/selfHarm-Bq4uGDZ1.png"},{"revision":null,"url":"assets/sendButton-DVsVPC1S.png"},{"revision":null,"url":"assets/Settings-Bs3gl52x.css"},{"revision":null,"url":"assets/settings-Du5Rj-be.png"},{"revision":null,"url":"assets/settings-DYTSD9ea.js"},{"revision":null,"url":"assets/Settings-kfgh95i-.js"},{"revision":null,"url":"assets/shake-B93L_x42.webp"},{"revision":null,"url":"assets/shieldbash-VF4uy7B1.png"},{"revision":null,"url":"assets/shieldbearer-IM3Gy17m.png"},{"revision":null,"url":"assets/ShoeLocker-CPPifg0h.js"},{"revision":null,"url":"assets/Shops-Gy5nL-na.js"},{"revision":null,"url":"assets/shopsicon-CxJUSSkR.webp"},{"revision":null,"url":"assets/Shrine-DLRHDKXG.js"},{"revision":null,"url":"assets/shrineicon-BvwFPa2C.webp"},{"revision":null,"url":"assets/shutup-CIkCyalS.png"},{"revision":null,"url":"assets/SingleShop-DQOflJVE.js"},{"revision":null,"url":"assets/sip-rPpH3tVl.png"},{"revision":null,"url":"assets/skyblueButtonBG-DdxC0zVJ.svg"},{"revision":null,"url":"assets/sleep-BWM8PiPn.png"},{"revision":null,"url":"assets/sliderBG-D7hjpQfe.png"},{"revision":null,"url":"assets/slingshot-DeA7PlH1.png"},{"revision":null,"url":"assets/social-aHez_M5M.png"},{"revision":null,"url":"assets/special-BNSh1yjW.png"},{"revision":null,"url":"assets/specialist-C84bVtuc.png"},{"revision":null,"url":"assets/speedcrafter-BB_5-GZE.png"},{"revision":null,"url":"assets/speedster-BOG4FcYf.png"},{"revision":null,"url":"assets/sports-Cga6f8Rf.png"},{"revision":null,"url":"assets/spray-C3x2ETFS.png"},{"revision":null,"url":"assets/squareBtnBlueBG-K8IjueNg.png"},{"revision":null,"url":"assets/stamina-LF5EZP0M.js"},{"revision":null,"url":"assets/stamina-Ls_Rw_Ob.png"},{"revision":null,"url":"assets/staminaRegen-CqqxDf8f.png"},{"revision":null,"url":"assets/standard-C2pZDbde.png"},{"revision":null,"url":"assets/StreetsPage-ChG-6rmU.js"},{"revision":null,"url":"assets/strength-DYbaT-zp.png"},{"revision":null,"url":"assets/strongbones-C5aVrH6O.png"},{"revision":null,"url":"assets/stun-6R21gpv7.png"},{"revision":null,"url":"assets/stunned-CUkux3PO.png"},{"revision":null,"url":"assets/Suggestions-CbHb737i.js"},{"revision":null,"url":"assets/surprise-D-SpDHVQ.gif"},{"revision":null,"url":"assets/talentData-CZzZ7076.js"},{"revision":null,"url":"assets/talents-CMBdjcAt.png"},{"revision":null,"url":"assets/TalentsView-XbE8uiam.js"},{"revision":null,"url":"assets/TalentTree-BUBhUAaT.js"},{"revision":null,"url":"assets/tasks-BUMSNglv.png"},{"revision":null,"url":"assets/tasksOld-Ba2_3B_A.png"},{"revision":null,"url":"assets/TasksPage-DlZ2trf4.js"},{"revision":null,"url":"assets/terraformerlogo-ad7GCudx.png"},{"revision":null,"url":"assets/thinking-Dj7UOHWy.png"},{"revision":null,"url":"assets/tongue-CqKru_nm.gif"},{"revision":null,"url":"assets/toxicdart-BYLi4Rvq.png"},{"revision":null,"url":"assets/TraderRep-BQlSqOPe.js"},{"revision":null,"url":"assets/Training-DiIONQN7.js"},{"revision":null,"url":"assets/trending-up-2reWf9Sx.js"},{"revision":null,"url":"assets/unique-CQIkJl13.png"},{"revision":null,"url":"assets/Updates-DBQbBpyk.js"},{"revision":null,"url":"assets/useGetInventory-BgvIMFcK.js"},{"revision":null,"url":"assets/useGetQuestObjectiveText-DrNzadky.js"},{"revision":null,"url":"assets/useGetUserSkills-D1K9h3Ou.js"},{"revision":null,"url":"assets/UsersTable-B_rLgqO9.js"},{"revision":null,"url":"assets/ViewGangModal-BJD_BV_x.js"},{"revision":null,"url":"assets/waa-DuIc44LN.png"},{"revision":null,"url":"assets/wallet-DMzdIN8s.js"},{"revision":null,"url":"assets/welcome-Bhq4nRFN.png"},{"revision":null,"url":"assets/wink-7gY75siX.gif"},{"revision":null,"url":"assets/workbox-window.prod.es5-B9K5rw8f.js"},{"revision":null,"url":"assets/yellowSliderFill-zVKZ4IKA.png"},{"revision":null,"url":"assets/yen-CBeGBZoQ.png"},{"revision":null,"url":"assets/YourJob-BDJQb0iI.js"},{"revision":null,"url":"assets/yup-BS27Th5G.gif"},{"revision":"a493ba0aa0b8ec8068d786d7248bb92c","url":"browserconfig.xml"},{"revision":"8da8c06dafa28f4103c5ed7984428fc4","url":"favicon.ico"},{"revision":"51fdff3fad2db8d8a32d582958f05769","url":"faviconOld.ico"},{"revision":"0f00ace76e5a7235292327e03ce6e3bc","url":"index.html"},{"revision":"15401a96d38afe4e8fb210f9a1e689df","url":"logo192.png"},{"revision":"92db626bf25065ee41aed8c0ba60c2dc","url":"logo512.png"},{"revision":"0e41d6d14d436ab1f2ae731cde86d524","url":"manifest.webmanifest"},{"revision":"fa1ded1ed7c11438a9b0385b1e112850","url":"robots.txt"},{"revision":"576ce7e02bfe2f9073959a42ad92d481","url":"safari-pinned-tab.svg"},{"revision":"951705c5ef0c47eeacc5f9a3b71443b7","url":"service-worker.js"},{"revision":"15401a96d38afe4e8fb210f9a1e689df","url":"logo192.png"},{"revision":"92db626bf25065ee41aed8c0ba60c2dc","url":"logo512.png"},{"revision":"0e41d6d14d436ab1f2ae731cde86d524","url":"manifest.webmanifest"}]);const si={apiKey:"AIzaSyCQ-QeWMrBZw8v9idybo_cFwAIlDO6j-z0",authDomain:"chikara-academy.firebaseapp.com",projectId:"chikara-academy",storageBucket:"chikara-academy.appspot.com",messagingSenderId:"75175802639",appId:"1:75175802639:web:a1cc5e6073f185ec46707a"},ii=Qe(si),ai=As(ii);ks(ai,t=>{console.log("[firebase-messaging-sw.js] Received background message",t)});self.skipWaiting();Fs();
