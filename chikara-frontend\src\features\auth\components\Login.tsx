import { usePersistStore } from "@/app/store/stores";
import useGameConfig from "@/hooks/useGameConfig";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";

import { signIn, signInDiscord, useSession } from "@/lib/auth-client";
import toast from "react-hot-toast";
import OauthSection from "../components/OauthSection";

export default function Login() {
    const navigate = useNavigate();
    const { data: session } = useSession();
    const { setEmail: saveEmail, email: authEmail } = usePersistStore();
    const [email, setEmail] = useState(authEmail);
    const [password, setPassword] = useState("");
    const [rememberMe, setRememberMe] = useState(authEmail.length > 0);
    const authConfig = useGameConfig();

    const login = async (e: React.FormEvent) => {
        e.preventDefault();

        try {
            const { data, error } = await signIn.email({
                email,
                password,
            });

            if (error) {
                // Handle error
                console.error("Login failed:", error.message);
                if (error.message) {
                    toast.error(error.message);
                    return;
                }
                toast.error("Server error! Try again later");
                return;
            }

            if (data) {
                if (rememberMe) {
                    saveEmail(email);
                } else {
                    saveEmail("");
                }

                navigate(`/callback?auth=credentials`);
            } else {
                console.log("error");
                toast.error("Server error! Try again later");
            }
        } catch (error) {
            toast.error("Server error! Try again later");
            console.log(error);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === "Enter") {
            e.preventDefault();
            login(e);
        }
    };

    if (authConfig?.LOGIN_DISABLED)
        return (
            <div className="px-4 py-8 text-shadow sm:px-10 md:py-8">
                <div className="flex flex-col sm:mx-auto sm:w-full sm:max-w-md ">
                    <h2 className="mb-2 text-center text-2xl text-gray-200 md:text-3xl">Login Disabled</h2>
                    <p className="mx-auto">
                        Please join the{" "}
                        <a className="text-blue-500" href="https://discord.gg/JhTmkxf4x4">
                            Discord
                        </a>{" "}
                        for any updates.
                    </p>
                </div>
            </div>
        );

    return (
        <div>
            <div className="max-h-full overflow-y-auto overflow-x-hidden px-4 pt-3 pb-6 md:px-6">
                <form className="space-y-3" action="#" method="POST">
                    <div>
                        <label htmlFor="email" className="block font-medium text-gray-300 text-sm">
                            Email address
                        </label>
                        <div className="mt-1">
                            <input
                                required
                                data-testid="login-email-input"
                                value={email}
                                id="email"
                                name="email"
                                type="email"
                                autoComplete="email"
                                className="block w-full appearance-none rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-gray-200 text-stroke-0 shadow-xs placeholder:text-gray-500 focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm"
                                onChange={(e) => {
                                    setEmail(e.target.value);
                                }}
                            />
                        </div>
                    </div>

                    <div>
                        <label htmlFor="password" className="block font-medium text-gray-300 text-sm">
                            Password
                        </label>
                        <div className="mt-1">
                            <input
                                required
                                data-testid="login-password-input"
                                id="password"
                                name="password"
                                type="password"
                                autoComplete="current-password"
                                className="block w-full appearance-none rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-gray-200 text-stroke-0 shadow-xs placeholder:text-gray-500 focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm"
                                onKeyDown={(e) => handleKeyDown(e)}
                                onChange={(e) => {
                                    setPassword(e.target.value);
                                }}
                            />
                        </div>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex items-center">
                            <input
                                defaultChecked={rememberMe}
                                id="remember_me"
                                name="remember_me"
                                type="checkbox"
                                className="size-4 rounded-sm border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-blue-600"
                                onChange={(e) => setRememberMe(e.target.checked)}
                            />
                            <label htmlFor="remember_me" className="ml-2 block text-gray-200 text-sm text-stroke-sm">
                                Remember me
                            </label>
                        </div>

                        <div className="text-sm">
                            <Link to="/forgotpassword" className="font-medium text-blue-500 hover:brightness-125">
                                Forgot your password?
                            </Link>
                        </div>
                    </div>
                    <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
                        <button
                            type="button"
                            data-testid="login-button"
                            className="darkBlueButtonBGSVG mx-auto flex h-15 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs hover:brightness-125 md:mt-3 dark:text-slate-200"
                            onClick={(e) => {
                                login(e);
                            }}
                        >
                            Login
                        </button>
                    </div>
                </form>

                <div className="-mb-1 mt-3 text-center text-sm">
                    Have an Alpha Key?{" "}
                    <Link to="/register" className="font-medium text-custom-yellow hover:brightness-125">
                        Register here
                    </Link>
                </div>
                <OauthSection />
            </div>
        </div>
    );
}
