import { cn } from "@/lib/utils";
import "./speechbubble.css";

// position = "t","b","l","r"
// animation = "pop", "float"
// shape = "round", "circle"

interface SpeechBubbleProps {
    text: string;
    animation?: "pop" | "float";
    shape?: "round" | "circle";
    position?: "t" | "b" | "l" | "r";
    className?: string;
}

export default function SpeechBubble({ text, animation, shape, position, className }: SpeechBubbleProps) {
    return <div className={cn("speech-bubble", animation, shape, position, className)}>{text}</div>;
}
