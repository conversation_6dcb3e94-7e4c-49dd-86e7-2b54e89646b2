import{u as E,a as v,b as L,c as p,j as e,D as w,d as M,f as N,e as _,g as D,r as y,h as u,R as z,U as k,i as F,k as O,l as I,m as q,n as R,A as B,E as K,s as Q,L as $,o as C,p as G,z as H,q as W,t as U,v as V,S as Y,w as A}from"./index-BNv9-hhu.js";import{A as J}from"./arrow-left-BgvakO9l.js";import{l as b}from"./menu-DMy37JpN.js";function X({sender:s,currentConvoId:d,currentUserId:r}){const i=s?.messages[0].senderId===r,a=Number.parseInt(s?.senderId),{data:n,isLoading:g,error:c}=E(a,{enabled:!!a}),m=v(),{data:x}=L(p.messaging.getChatHistory.queryOptions()),o=(x?.filter(h=>h.senderId===a||h.receiverId===a)||[]).filter(h=>h.message!=="<NewConversation>")[0]?.message||"",l=h=>{m(`/inbox/${h}`)};if(g)return null;if(c)return"An error has occurred: "+c.message;const j=(h,P)=>Number.parseInt(s?.senderId)===Number.parseInt(P||"0")?"bg-slate-800 md:bg-slate-900 brightness-[1.3]":h===!1&&!i?"bg-blue-950":"bg-slate-800 md:bg-slate-900 hover:brightness-[1.2]";return e.jsxs("li",{className:`relative flex h-20 flex-row gap-4 px-4 py-2 ${j(s.messages[0].read,d)}`,children:[e.jsx(w,{className:"my-auto h-12 w-auto rounded-full",src:n}),e.jsxs("div",{className:"my-auto w-5/6 md:my-0",children:[e.jsxs("div",{className:"flex justify-between space-x-3",children:[e.jsx("div",{className:"min-w-0 flex-1",children:e.jsxs("button",{className:"block focus:outline-hidden",onClick:()=>n?.id&&l(n.id),children:[e.jsx("span",{className:"absolute inset-0","aria-hidden":"true"}),e.jsx("p",{className:M("truncate text-sm text-stroke-sm",n?.userType==="admin"?"text-red-500":"text-blue-500"),children:n?.username})]})}),e.jsx("time",{dateTime:s.messages[0].createdAt,className:"shrink-0 whitespace-nowrap text-blue-300 text-xs",children:N(new Date(s.messages[0].createdAt),"p")})]}),e.jsx("div",{className:"mt-1 font-medium font-body",children:e.jsx("p",{className:M(n?.userType==="admin"?"text-red-300":"text-gray-100","line-clamp-2 text-sm"),children:o})})]})]},a)}function S({message:s,type:d="sentMessage",convoUserInfo:r,currentUser:i}){const a=_(),n=d==="sentMessage",g=v(),{mutate:c}=D(p.messaging.markMessageRead.mutationOptions({onSuccess:()=>{a.invalidateQueries({queryKey:p.messaging.getChatHistory.key()}),a.invalidateQueries({queryKey:p.messaging.getUnreadCount.key()})},onError:x=>{console.error("Failed to mark message as read:",x)}}));if(y.useEffect(()=>{s?.read===!1&&c({messageId:s.id})},[s?.read,c]),!s)return null;const m=s.isGlobal&&r?.userType==="admin";return e.jsxs("li",{className:u("relative flex min-w-40 gap-5 py-2 md:px-3",n?"flex-row-reverse self-end":"flex-row self-start ",s?.message?.length<6&&"text-center"),children:[e.jsx(w,{className:"my-auto h-12 w-auto cursor-pointer rounded-full",src:n?i:r,onClick:()=>g(`/profile/${n?i?.id:r?.id}`)}),e.jsxs("div",{className:u("group relative flex size-full min-h-14 flex-col justify-start rounded-lg border px-3 pt-2 pb-5 shadow-xl",n?"border-indigo-500 bg-indigo-800":"border-gray-500 bg-gray-700",m&&"bg-blue-800! border-blue-500!"),children:[s.isGlobal&&e.jsx("div",{className:u(r?.userType==="admin"?"-top-7 text-red-500 text-sm":"-top-5 text-blue-500 text-xs","-translate-x-1/2 absolute right-[40%]"),children:r?.userType==="admin"?"Announcement":"Megaphone"}),e.jsx("div",{className:u(n?"message_arrow_right":"message_arrow_left")}),e.jsx("div",{className:u(m?"text-red-200":"text-gray-50","whitespace-pre-wrap text-sm"),children:e.jsx(z,{msg:s})}),e.jsx("div",{className:u("-bottom-[1.1rem] absolute",n?"right-1 text-right":"left-1 text-left"),children:e.jsxs("p",{"data-tooltip-id":"date-tooltip","data-tooltip-content":N(new k(s.createdAt),"PP, p"),className:"cursor-pointer whitespace-nowrap font-body font-semibold text-slate-300 text-stroke-0 text-xs sm:mt-0",children:[e.jsx("time",{className:"hidden group-hover:block",dateTime:s.createdAt,children:N(new k(s.createdAt),"PP, p")}),e.jsxs("time",{className:"group-hover:hidden",dateTime:s.createdAt,children:[F(s.createdAt)," ago"]})]})})]})]},s.id)}function Z({userId:s,currentUser:d}){const[r,i]=y.useState(""),[a,n]=y.useState(!1),g=_(),[c,m]=y.useState(!1),x=O(()=>{m(!1)}),f=D(p.messaging.sendMessage.mutationOptions({onSuccess:()=>{i(""),g.invalidateQueries({queryKey:p.messaging.getChatHistory.key()})},onError:l=>{console.error("Sending message failed:",l),I.error(l.message||"Failed to send message")}})),t=()=>{r.length>0&&s?f.mutate({userId:s,message:r}):(n(!0),I.error("Message can't be blank!"),setTimeout(()=>n(!1),2e3))},o=l=>{l.key==="Enter"&&(l.preventDefault(),t())};return e.jsx("div",{className:u(a?"chatMessageBoxError":"chatMessageBox","-translate-x-1/2 absolute bottom-0 left-1/2 mb-4 flex w-[96%] justify-between rounded-md text-center text-sm shadow-xs md:my-3 md:w-5/6 dark:bg-[#15121C]"),children:e.jsxs("div",{className:"flex w-full flex-row",children:[e.jsx("textarea",{required:!0,id:"privateMessage",name:"privateMessage",maxLength:d?.userType==="admin"?2e3:200,rows:2,cols:1,className:"chatTextArea scrollbar h-16 whitespace-pre-wrap rounded-md border-none bg-[#dee2e9] p-2 font-mono text-sm md:h-auto dark:bg-[#15121C] dark:text-white",placeholder:"Your message...",value:r,onChange:l=>i(l.target.value),onKeyDown:l=>o(l)}),e.jsx("div",{className:"relative mx-1 my-auto ml-auto",onClick:l=>{l.stopPropagation(),m(j=>!j)},children:e.jsx(T,{children:e.jsx("img",{src:R,alt:"",className:"size-6 fill-white text-white"})})}),e.jsx(B,{children:c&&e.jsx(K,{userMessage:r,setUserMessage:i,innerRef:x})}),e.jsx("div",{className:"relative my-auto mr-2 ml-1",onClick:t,children:e.jsx(T,{children:e.jsx("img",{src:Q,alt:"",className:"h-5 w-4 fill-white text-white"})})})]})})}const T=q.memo(({children:s})=>e.jsx("button",{className:"size-9 cursor-pointer rounded-md border-[#1F1F2D] border-b bg-[#28287c] shadow-[0_1px_0_0_#303045_inset,0_2px_2px_0_rgba(0,0,0,0.25)] transition hover:brightness-110",children:e.jsx("div",{className:"flex items-center justify-center",children:s})}));function ee({sortedArray:s,sentMessages:d,currentUser:r,convoId:i}){const{data:a,isLoading:n}=E(i?Number.parseInt(i):0,{enabled:!!i}),g=v(),m=(d||[]).filter(t=>t.receiverId===Number.parseInt(a?.id?.toString()||"0")),x=s.filter(t=>t.senderId===i),f=m.concat(x[0]?.messages||[]);return f.sort((t,o)=>t.createdAt<o.createdAt?1:-1),e.jsx("section",{"aria-labelledby":"message-heading",className:"flex h-full min-w-0 flex-1 flex-col overflow-hidden",children:i?e.jsx($,{size:14,isLoading:n,children:e.jsxs("div",{className:"relative flex min-h-0 flex-1 flex-col",children:[e.jsx("div",{className:"bg-gray-800 pt-2 pb-3 shadow-sm",children:e.jsxs("div",{className:"flex items-baseline px-4 sm:px-6 lg:px-8",children:[e.jsx(J,{className:"my-auto mr-5 size-7 cursor-pointer md:hidden text-gray-200",onClick:()=>g("/inbox")}),e.jsx(C,{to:`/profile/${a?.id}`,children:e.jsx("div",{className:"my-auto mr-3",children:e.jsx(w,{className:"w-10 rounded-full md:w-14",src:a})})}),e.jsxs("div",{className:"my-auto sm:w-0 sm:flex-1",children:[e.jsx(C,{to:`/profile/${a?.id}`,children:e.jsxs("h1",{id:"message-heading",className:"font-medium text-custom-yellow text-sm md:text-lg",children:[a?.username," ",e.jsxs("span",{className:"text-gray-400 text-sm",children:["#",a?.id]})]})}),e.jsx("p",{className:"mt-1 truncate text-red-400 text-xs",children:a?.gang?.name||"No Gang"})]}),e.jsx(se,{convoUserInfo:a})]})}),e.jsx("ul",{className:"flex flex-1 flex-col-reverse gap-4 overflow-y-auto p-4 pb-16 font-body md:pb-14 lg:px-8",children:f.map(t=>t?.senderId===r?.id?e.jsx(S,{type:"sentMessage",message:t,convoUserInfo:a,currentUser:r},t?.id):e.jsx(S,{type:"receivedMessage",message:t,convoUserInfo:a,currentUser:r},t?.id))}),e.jsx("div",{className:"h-10 w-full border-gray-600 border-t bg-gray-700 ",children:e.jsx(Z,{userId:a?.id,currentUser:r})})]})}):e.jsx("div",{className:"flex size-full",children:e.jsx("div",{className:"m-auto text-2xl text-gray-300",children:"Select a message"})})})}const se=({convoUserInfo:s})=>s?e.jsxs(b,{as:"div",className:"ml-3 inline-block text-left",children:[e.jsx("div",{children:e.jsxs(b.Button,{className:"absolute top-3 right-5 rounded-full p-2 text-gray-400 hover:text-gray-600 focus:outline-hidden focus:ring-2 focus:ring-blue-600 md:top-5",children:[e.jsx("span",{className:"sr-only",children:"Open options"}),e.jsx(G,{className:"size-5","aria-hidden":"true"})]})}),e.jsx(H,{as:y.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:e.jsx(b.Items,{className:"absolute right-0 z-150 mt-2 w-56 origin-top-right rounded-md border border-gray-400 bg-gray-900 shadow-lg ring-1 ring-black/5 focus:outline-hidden",children:e.jsxs("div",{className:"py-1",children:[e.jsx(b.Item,{children:({active:d})=>e.jsx("button",{type:"button",className:u(d?"bg-gray-700 text-gray-200":"text-gray-200","flex w-full justify-between px-4 py-2 text-sm"),children:e.jsxs("span",{children:["Block ",s?.username]})})}),e.jsx(b.Item,{children:({active:d})=>e.jsx("a",{href:"#",className:u(d?"bg-gray-700 text-gray-200":"text-gray-200","flex justify-between px-4 py-2 text-sm"),children:e.jsxs("span",{children:["Report ",s?.username]})})}),e.jsx(b.Item,{children:({active:d})=>e.jsx("a",{href:"#",className:u(d?"bg-gray-700 text-gray-200":"text-gray-200","flex justify-between px-4 py-2 text-sm"),children:e.jsx("span",{children:"Delete conversation"})})})]})})})]}):null;function ne(){const{id:s}=W(),{isLoading:d,error:r,data:i}=L(p.messaging.getChatHistory.queryOptions()),{data:a,isLoading:n}=U(),g=V(),c=i?.reduce((t,o)=>{const l=String(o.senderId===a?.id?o.receiverId:o.senderId);return t[l]||(t[l]=[]),t[l].push(o),t},{}),m=[];for(const t in c)m.push({senderId:t===String(a?.id)?String(c[t][0].receiverId):t,messages:c[t],unreadTotal:c[t].filter(o=>o.read===!1).length});const x=m.sort((t,o)=>t.messages[0].createdAt<o.messages[0].createdAt?1:-1),f=x.filter(t=>Number.parseInt(t.senderId)===a?.id)||[];return d||n?e.jsx(Y,{center:!0}):r?"An error has occurred: "+r.message:e.jsxs("div",{className:"fixed flex h-[calc(100dvh-8.75rem)] w-full flex-col md:static md:h-[65dvh] -m-2",children:[e.jsxs("div",{className:u("relative flex h-12 gap-3 border-gray-900 border-y-2 bg-[#343549] p-1 px-4 pb-1.5 md:hidden md:rounded-t-lg"),children:[e.jsx("div",{className:"relative my-auto h-6 w-auto",children:e.jsx("img",{className:"h-6 w-auto",src:A,alt:""})}),e.jsx("h1",{className:"my-auto font-medium text-lg text-stroke-s-sm text-white",children:"Inbox"}),e.jsx("div",{className:"absolute bottom-0 left-0 h-1 w-full bg-[#272839]"})]}),e.jsx("div",{className:"flex min-h-0 flex-1 overflow-hidden",children:!s&&i?.length===0?e.jsx("h2",{className:"mx-auto mt-12 text-center text-2xl dark:text-shadow-lg dark:text-slate-200",children:"No Messages to show!"}):e.jsxs("main",{className:"min-w-0 flex-1 border-gray-600 md:border xl:flex",children:[g&&!s?null:e.jsx(ee,{sortedArray:x,sentMessages:f[0]?.messages,currentUser:a,convoId:s}),e.jsx("aside",{className:"xl:order-first xl:block xl:shrink-0",children:e.jsxs("div",{className:"relative flex h-full flex-col border-gray-600 bg-gray-800 md:w-64 md:border-r 2xl:w-96",children:[e.jsx("div",{className:"shrink-0",children:e.jsx("div",{className:"hidden h-14 flex-col justify-center bg-gray-900 px-6 text-stroke-sm md:flex",children:e.jsxs("div",{className:"flex items-baseline space-x-3",children:[e.jsx("img",{className:"my-auto h-6 w-auto",src:A,alt:""}),e.jsx("h2",{className:"font-medium text-gray-200 text-xl",children:"Inbox"})]})})}),e.jsx("nav",{"aria-label":"Message list",className:"min-h-0 flex-1 overflow-y-auto",children:e.jsx("ul",{className:"divide-y divide-gray-600 border-gray-600 border-y",children:x.map(t=>e.jsx(y.Fragment,{children:e.jsx(X,{sender:t,currentConvoId:s,currentUserId:a?.id})},t.senderId))})})]})})]})})]})}export{ne as default};
