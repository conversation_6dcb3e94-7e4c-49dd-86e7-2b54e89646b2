import{j as r,r as c,h as t}from"./index-BNv9-hhu.js";function x({id:e,color:o}){return r.jsxs(r.Fragment,{children:[r.jsxs("defs",{children:[r.jsx(l,{id:`${e}-gradient`,color:o,gradientTransform:"rotate(65.924 1.519 20.92) scale(25.7391)"}),r.jsx(l,{id:`${e}-gradient-dark`,color:o,gradientTransform:"matrix(0 24.5 -24.5 0 16 5.5)"})]}),r.jsxs(f,{children:[r.jsx("circle",{cx:20,cy:20,r:12,fill:`url(#${e}-gradient)`}),r.jsx("path",{d:"M3 16c0 7.18 5.82 13 13 13s13-5.82 13-13S23.18 3 16 3 3 8.82 3 16Z",fillOpacity:.5,className:"fill-(--icon-background) stroke-(--icon-foreground)",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}),r.jsx("path",{d:"m15.408 16.509-1.04-5.543a1.66 1.66 0 1 1 3.263 0l-1.039 5.543a.602.602 0 0 1-1.184 0Z",className:"fill-(--icon-foreground) stroke-(--icon-foreground)",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}),r.jsx("path",{d:"M16 23a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z",fillOpacity:.5,stroke:"currentColor",className:"fill-(--icon-background) stroke-(--icon-foreground)",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})]}),r.jsx(p,{children:r.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2 16C2 8.268 8.268 2 16 2s14 6.268 14 14-6.268 14-14 14S2 23.732 2 16Zm11.386-4.85a2.66 2.66 0 1 1 5.228 0l-1.039 5.543a1.602 1.602 0 0 1-3.15 0l-1.04-5.543ZM16 20a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z",fill:`url(#${e}-gradient-dark)`})})]})}const u={warning:x},m={blue:"[--icon-foreground:var(--color-slate-900)] [--icon-background:var(--color-white)]",amber:"[--icon-foreground:var(--color-amber-900)] [--icon-background:var(--color-amber-100)]"};function k({color:e="blue",icon:o,className:n,...s}){const a=c.useId(),i=u[o];return r.jsx("svg",{"aria-hidden":"true",viewBox:"0 0 32 32",fill:"none",className:t(n,m[e]),...s,children:r.jsx(i,{id:a,color:e})})}const g={blue:[{stopColor:"#0EA5E9"},{stopColor:"#22D3EE",offset:".527"},{stopColor:"#818CF8",offset:1}],amber:[{stopColor:"#FDE68A",offset:".08"},{stopColor:"#F59E0B",offset:".837"}]};function l({color:e="blue",...o}){return r.jsx("radialGradient",{cx:0,cy:0,r:1,gradientUnits:"userSpaceOnUse",...o,children:g[e].map((n,s)=>r.jsx("stop",{...n},s))})}function f({className:e,...o}){return r.jsx("g",{className:t("dark:hidden",e),...o})}function p({className:e,...o}){return r.jsx("g",{className:t("hidden dark:inline",e),...o})}const d={note:{container:"bg-sky-50 dark:bg-slate-800/60 dark:ring-1 dark:ring-slate-300/10",title:"text-sky-900 dark:text-sky-400",body:"text-sky-800 [--tw-prose-background:var(--color-sky-50)] prose-a:text-sky-900 prose-code:text-sky-900 dark:text-slate-300 dark:prose-code:text-slate-300"},warning:{container:"bg-amber-50 dark:bg-slate-800/60 dark:ring-1 dark:ring-slate-300/10",title:"text-amber-900 dark:text-amber-500",body:"text-amber-800 [--tw-prose-underline:var(--color-amber-400)] [--tw-prose-background:var(--color-amber-50)] prose-a:text-amber-900 prose-code:text-amber-900 dark:text-slate-300 dark:[--tw-prose-underline:var(--color-sky-700)] dark:prose-code:text-slate-300"}},b={warning:e=>r.jsx(k,{icon:"warning",color:"amber",...e})};function h({type:e="warning",title:o,children:n,className:s,titleSize:a}){const i=b[e];return r.jsxs("div",{className:t(s,"mx-4 my-2 flex rounded-3xl p-3 md:mx-0",d[e].container),children:[r.jsx(i,{className:"size-8 flex-none"}),r.jsxs("div",{className:"ml-4 flex-auto",children:[r.jsx("p",{className:t(a||"text-base md:text-xl","m-0 font-display",d[e].title),children:o}),n&&r.jsx("div",{className:t("prose mt-2 font-body text-sm md:text-base",d[e].body),children:n})]})]})}export{h as C};
