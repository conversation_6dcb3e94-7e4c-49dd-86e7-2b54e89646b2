import Spinner from "@/components/Spinners/Spinner";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { format } from "date-fns";
import { useBankTransactions } from "../api/useBankTransactions";
import BankTransfer from "./BankTransfer";
import { formatCurrency } from "@/utils/currencyHelpers";

interface Transaction {
    transaction_type: string;
    cash: number;
    createdAt: Date;
    bankBalance: number;
    playerId?: number;
    initiatorId?: string;
    secondPartyId?: string;
}

interface TransactionHistoryProps {
    historyLimit?: number;
}

const TransactionHistory = ({ historyLimit = 10 }: TransactionHistoryProps) => {
    const isMobile = useCheckMobileScreen();
    const { isLoading, error, data: rawData } = useBankTransactions();
    const { data: currentUser } = useFetchCurrentUser();

    // Limit the data to the specified number of transactions
    const data = rawData ? rawData.slice(0, historyLimit) : [];

    if (isLoading) return "Loading...";

    if (error) return "An error has occurred: " + error.message;

    const formatDate = (date: Date) => {
        // const parseDate = parseISO(date);
        let formattedDate: string;
        if (isMobile) {
            formattedDate = format(date, "dd/MM kk:mm");
        } else {
            formattedDate = format(date, "dd/MM/y kk:mm");
        }
        return formattedDate;
    };

    const formatTransaction = (transaction: Transaction) => {
        if (transaction.transaction_type === "bank_withdrawl") {
            return "Bank Withdrawal";
        } else if (transaction.transaction_type === "bank_deposit") {
            return "Bank Deposit";
        }
        return transaction.transaction_type;
    };

    const formatTransactionAmount = (transaction: Transaction) => {
        if (transaction.transaction_type === "bank_transfer") {
            if (transaction.playerId === currentUser?.id) {
                return (
                    <td className="whitespace-nowrap p-2 text-green-500 text-sm">
                        +{formatCurrency(transaction.cash)}
                    </td>
                );
            } else {
                return (
                    <td className="whitespace-nowrap p-2 text-red-500 text-sm">-{formatCurrency(transaction.cash)}</td>
                );
            }
        } else {
            return (
                <td className="whitespace-nowrap p-2 text-gray-900 text-sm dark:text-gray-300">
                    {formatCurrency(transaction.cash)}
                </td>
            );
        }
    };

    return (
        <div className="mb-5 px-4 sm:px-6 lg:px-8">
            <div className="sm:flex sm:items-center">
                <div className="sm:flex-auto">
                    <h1 className="font-semibold text-gray-900 md:text-xl dark:font-normal dark:text-gray-200 dark:text-stroke-s-sm">
                        Transaction History (Last {historyLimit} Transactions)
                    </h1>
                </div>
            </div>
            <div className="mt-4 flex flex-col md:mt-8">
                <div className="-mx-4 -my-2 sm:-mx-6 lg:-mx-8 overflow-x-auto">
                    <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                        <div className="overflow-hidden shadow-sm ring-1 ring-black/5 md:rounded-lg">
                            {isLoading ? (
                                <Spinner />
                            ) : (
                                <table className="min-w-full divide-y divide-gray-300">
                                    <thead className="bg-gray-50 text-gray-900 text-sm dark:bg-gray-800 dark:text-gray-200 dark:text-stroke-sm">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="whitespace-nowrap py-3.5 pr-3 pl-4 text-left font-semibold sm:pl-6 dark:font-normal"
                                            >
                                                Date
                                            </th>
                                            <th
                                                scope="col"
                                                className="whitespace-nowrap px-2 py-3.5 text-left font-semibold dark:font-normal"
                                            >
                                                Type
                                            </th>
                                            <th
                                                scope="col"
                                                className="whitespace-nowrap px-2 py-3.5 text-left font-semibold dark:font-normal"
                                            >
                                                Amount
                                            </th>

                                            {/* <th
                        scope="col"
                        className="hidden whitespace-nowrap px-2 py-3.5 text-left font-semibold md:table-cell dark:font-normal font-semibold"
                      >
                        Cash Balance
                      </th> */}
                                            <th
                                                scope="col"
                                                className="hidden whitespace-nowrap px-2 py-3.5 text-left font-semibold md:table-cell dark:font-normal"
                                            >
                                                Bank Balance
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-600 dark:bg-gray-700">
                                        {data.map((transaction, i) => (
                                            <tr key={i}>
                                                <td className="whitespace-nowrap py-2 pr-3 pl-4 text-gray-500 text-sm sm:pl-6 md:pr-1 dark:text-gray-300">
                                                    {formatDate(transaction.createdAt)}
                                                </td>
                                                {transaction.transaction_type === "bank_transfer" ? (
                                                    <BankTransfer
                                                        transaction={transaction}
                                                        currentUser={currentUser?.id?.toString() || ""}
                                                    />
                                                ) : (
                                                    <td className="whitespace-nowrap p-2 font-medium text-gray-900 text-sm dark:text-gray-300">
                                                        {formatTransaction(transaction)}
                                                    </td>
                                                )}

                                                {formatTransactionAmount(transaction)}
                                                {/* <td className="hidden whitespace-nowrap px-2 py-2 text-sm text-gray-500 md:table-cell">
                          ¥{transaction.cashBalance}
                        </td> */}
                                                <td className="hidden whitespace-nowrap p-2 text-gray-900 text-sm md:table-cell dark:text-gray-300">
                                                    {formatCurrency(transaction.bankBalance)}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TransactionHistory;
