import{b as s,c as i,C as p,j as o,h as n}from"./index-BNv9-hhu.js";function m({shopId:e,heartWidth:r}){const{data:t}=s(i.shop.getTraderRep.queryOptions({input:{shopId:Number(e)},enabled:!!e&&!isNaN(Number(e))&&Number(e)>0})),{MAX_TRADER_REP:a}=p();return o.jsx(o.Fragment,{children:[...new Array(a||4)].map((d,l)=>o.jsxs("div",{className:n(u(l,t),"relative"),children:[o.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:n(r,"stroke-2 stroke-gray-800 text-gray-600/50 drop-shadow-md"),viewBox:"0 0 20 20",fill:"currentColor",children:o.jsx("path",{fillRule:"evenodd",d:"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z",clipRule:"evenodd"})},l),o.jsx("svg",{style:{clipPath:c(l+1,t)},xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:n(r,"absolute top-0 left-0 stroke-2 stroke-gray-800 text-pink-600"),children:o.jsx("path",{fillRule:"evenodd",d:"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z",clipRule:"evenodd"})})]},l))})}const c=(e,r)=>{if(!r)return"polygon(0 0, 100% 0, 100% 100%, 0 100%)";const t=r[0]?.reputationLevel;if(!t)return"polygon(0 0, 0% 0, 0% 100%, 0 100%)";const a=Math.max(0,(t-e+1)*100);return`polygon(0 0, ${a}% 0, ${a}% 100%, 0 100%)`},u=(e,r)=>{if(!r)return"opacity-50";const t=r[0]?.reputationLevel;return!t||t<1?e===0?"opacity-100":"opacity-50":t>=e?"opacity-100":"opacity-50"};export{m as T};
