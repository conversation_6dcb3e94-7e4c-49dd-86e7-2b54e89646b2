import{C as f,j as e,S as D,i as T,b as v,c as k,o as u,D as b,h as c}from"./index-BNv9-hhu.js";import{g as y,s as p,b as S}from"./Icon_ImageIcon_Medal_Silver-OH_4ykaO.js";const w=(a={})=>v(k.leaderboards.getLeaderBoards.queryOptions({staleTime:3e5,...a}));function C(){const a=f(),{isLoading:l,error:d,data:r}=w(),t=r?.data;if(a?.LEADERBOARDS_DISABLED)return e.jsx("div",{className:"mt-10 flex flex-col dark:text-slate-200",children:e.jsxs("div",{className:"mx-auto text-center",children:[e.jsx("h2",{className:"text-xl",children:"Leaderboards currently Disabled"}),e.jsx("p",{children:"Please return later."})]})});const i=r?.lastFetch?r.lastFetch.toString():null;return d||!l&&!t?"Failed to fetch leaderboards":e.jsx(e.Fragment,{children:l?e.jsx(D,{center:!0}):e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"lg:-mt-2 my-0.5 text-right text-sm lg:mb-1",children:i&&`Last Updated: ${T(i)} ago`}),e.jsxs("div",{className:"grid 3xl:grid-cols-4 grid-cols-1 gap-4 text-shadow md:mx-auto md:max-w-(--breakpoint-2xl) md:grid-rows-2 xl:grid-cols-2 2xl:grid-cols-3",children:[e.jsx(n,{hideStats:!0,leaderboardData:t?.totalStats,attributeName:"strength",leaderTitleText:"Most Stats",atrributeDisplayName:"Total Stats"}),e.jsx(n,{hideStats:!0,leaderboardData:t?.hench,attributeName:"strength",leaderTitleText:"Strongest Student",atrributeDisplayName:"Strength"}),e.jsx(n,{hideStats:!0,leaderboardData:t?.defensive,attributeName:"defence",leaderTitleText:"Most Defensive",atrributeDisplayName:"Defence"}),e.jsx(n,{hideStats:!0,leaderboardData:t?.dexterous,attributeName:"dexterity",leaderTitleText:"Most Dexterous",atrributeDisplayName:"Dexterity"}),e.jsx(n,{hideStats:!0,leaderboardData:t?.intelligent,attributeName:"intelligence",leaderTitleText:"Most Intelligent",atrributeDisplayName:"Intelligence"}),e.jsx(n,{hideStats:!0,leaderboardData:t?.endurance,attributeName:"endurance",leaderTitleText:"Most Endurance",atrributeDisplayName:"Endurance"}),e.jsx(n,{hideStats:!0,leaderboardData:t?.vitality,attributeName:"vitality",leaderTitleText:"Most Vitality",atrributeDisplayName:"Vitality"}),e.jsx(s,{leaderboardData:t?.pvpwins,leaderTitleText:"Most Feared Fighter",attributeName:"battleWins",atrributeDisplayName:"PvP Wins",hideStats:!1}),e.jsx(s,{leaderboardData:t?.level,leaderTitleText:"Biggest Grinder",attributeName:"level",atrributeDisplayName:"Level",hideStats:!1}),e.jsx(s,{hideStats:!0,leaderboardData:t?.money,leaderTitleText:"Richest Oligarch",attributeName:"bank_balance",atrributeDisplayName:"Money"}),e.jsx(s,{leaderboardData:t?.zones,attributeName:"roguelikeMapsCompleted",leaderTitleText:"Highest Zone Reached",atrributeDisplayName:"Zone",hideStats:!1}),e.jsx(s,{hideStats:!0,leaderboardData:t?.joblevel,attributeName:"joblevel",leaderTitleText:"Highest Job Level",atrributeDisplayName:"Job Level"}),e.jsx(s,{leaderboardData:t?.crafts,attributeName:"craftsCompleted",leaderTitleText:"Most items crafted",atrributeDisplayName:"Items Crafted",hideStats:!1}),e.jsx(s,{leaderboardData:t?.npcwins,attributeName:"npcBattleWins",leaderTitleText:"Most NPCs defeated",atrributeDisplayName:"NPC Wins",hideStats:!1}),e.jsx(s,{leaderboardData:t?.quests,attributeName:"questsCompleted",leaderTitleText:"Most tasks completed",atrributeDisplayName:"Tasks complete",hideStats:!1}),e.jsx(s,{leaderboardData:t?.muggingGain,attributeName:"totalMuggingGain",leaderTitleText:"Most mugging gains",atrributeDisplayName:"Gained from mugging"}),e.jsx(s,{isNegative:!0,leaderboardData:t?.muggingLoss,attributeName:"totalMuggingLoss",leaderTitleText:"Most mugging losses",atrributeDisplayName:"Lost from mugging"}),e.jsx(s,{leaderboardData:t?.casinoWinner,attributeName:"totalCasinoProfitLoss",leaderTitleText:"Best Gambler",atrributeDisplayName:"Casino Profit"}),e.jsx(s,{isRed:!0,leaderboardData:t?.casinoLoser,attributeName:"totalCasinoProfitLoss",leaderTitleText:"Worst Gambler",atrributeDisplayName:"Casino Loss"}),e.jsx(s,{leaderboardData:t?.totalBountyRewards,attributeName:"totalBountyRewards",leaderTitleText:"Total Bounty Claimed",atrributeDisplayName:"Bounty Claimed"}),e.jsx(s,{leaderboardData:t?.totalMissionHours,attributeName:"totalMissionHours",leaderTitleText:"Total Mission Hours",atrributeDisplayName:"Mission Hours"}),e.jsx(s,{leaderboardData:t?.marketItemsSold,attributeName:"marketItemsSold",leaderTitleText:"Market Items Sold",atrributeDisplayName:"Items Sold"}),e.jsx(s,{leaderboardData:t?.marketMoneyMade,attributeName:"marketMoneyMade",leaderTitleText:"Market Money Made",atrributeDisplayName:"Yen Made"})]})]})})}const g=(a,l)=>{if(a.user_achievements&&l in a.user_achievements){const r=a.user_achievements[l];return typeof r=="number"?r:0}const d=a[l];return typeof d=="number"?d:0};function s(a){const{USERS_PER_BOARD:l}=f();if(!a?.leaderboardData?.length)return null;if(Object.keys(a?.leaderboardData)?.length<1)return;const d=a.leaderboardData.slice(1),r=a.attributeName,t=a.atrributeDisplayName,i=a.leaderTitleText,h=a.hideStats,m=a.isNegative,N=a.isRed,x=a.leaderboardData[0];return e.jsx("div",{className:"row-span-2 m-1 md:m-0",children:e.jsxs("div",{className:"flex w-full flex-col overflow-hidden rounded-md bg-white pt-4 pb-6 shadow-sm dark:bg-gray-800",children:[e.jsx("p",{className:"-mt-4 mb-3 flex h-10 truncate pb-2 text-center font-semibold text-gray-900 text-md tracking-wide xl:text-lg dark:bg-indigo-600 dark:font-medium dark:text-slate-100 dark:text-stroke-sm",children:e.jsx("span",{className:"mx-auto mt-2 md:mt-1.5",children:i})}),e.jsxs("div",{className:"m-auto flex flex-row gap-5 pr-6",children:[e.jsx("img",{className:"h-14 w-16",src:y,alt:"Gold medal"}),e.jsx(u,{to:`/profile/${x.id}`,children:e.jsx(b,{className:"size-14 rounded-md ring-2 ring-indigo-500",src:x})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("p",{className:c("text-green-600","font-semibold text-lg dark:font-medium dark:text-stroke-sm"),children:x.username}),e.jsxs("p",{className:"text-gray-900 text-lg dark:text-slate-200 dark:text-stroke-sm",children:[t,":"," ",e.jsxs("span",{className:c(m||N?"text-red-500":"text-green-500"),children:[m?" -":"",h?"?":g(x,r)]})]})]})]}),e.jsx("div",{className:"mx-3 mt-6 flex flex-col",children:e.jsx("div",{className:"inline-block w-full align-middle",children:e.jsx("div",{className:"overflow-hidden rounded-md border-gray-200 border-b shadow-sm dark:border-gray-800",children:e.jsx("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-800",children:e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-700",children:d.slice(0,l).map((o,j)=>e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsxs("div",{className:"mr-5 flex size-10",children:[j===0&&e.jsx("img",{className:"m-auto size-10",src:p,alt:"Silver medal"}),j===1&&e.jsx("img",{className:"ml-0.5 h-10 w-8",src:S,alt:"Bronze medal"})]}),e.jsx("div",{className:"size-10 shrink-0",children:e.jsx(u,{to:`/profile/${o.id}`,children:e.jsx(b,{className:"size-10 rounded-full",src:o,loading:"lazy"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-gray-900 text-sm dark:text-slate-100 dark:text-stroke-sm",children:o.username}),e.jsxs("div",{className:"text-gray-500 text-sm dark:text-slate-400 dark:text-stroke-sm",children:["Student #",o.id]})]})]})}),e.jsx("td",{className:"whitespace-nowrap px-10 py-4",children:e.jsxs("div",{className:c(m||N?"text-red-500":"text-green-500","text-sm dark:text-stroke-sm"),children:[m?" -":"",h?"":g(o,r)]})})]},o.id))})})})})})]})})}function n(a){if(!a?.leaderboardData?.length)return null;const l=a.attributeName,d=a.atrributeDisplayName,r=a.leaderTitleText,t=a.hideStats,i=a.leaderboardData[0];return e.jsx("div",{className:"m-1 md:m-0",children:e.jsxs("div",{className:"flex w-full flex-col overflow-hidden rounded-md bg-white pt-4 pb-6 shadow-sm dark:bg-gray-800",children:[e.jsx("p",{className:"-mt-4 mb-3 flex h-10 truncate pb-2 text-center font-semibold text-gray-900 text-md tracking-wide xl:text-lg dark:bg-indigo-600 dark:font-medium dark:text-slate-100 dark:text-stroke-sm",children:e.jsx("span",{className:"mx-auto mt-2 md:mt-1.5",children:r})}),e.jsxs("div",{className:"m-auto flex flex-row gap-5 pr-6",children:[e.jsx("img",{className:"h-14 w-16",src:y,alt:"Gold medal"}),e.jsx(u,{to:`/profile/${i.id}`,children:e.jsx(b,{className:"size-14 rounded-md ring-2 ring-indigo-500",src:i})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("p",{className:c("text-green-600","font-semibold text-lg dark:font-medium dark:text-stroke-sm"),children:i.username}),e.jsxs("p",{className:"text-gray-900 text-lg dark:text-slate-200 dark:text-stroke-sm",children:[d,": ",t?"?":g(i,l)]})]})]})]})})}export{C as default};
