import{H as N,b as j,c as a,e as p,g as y,l as d,y as b,t as v,j as e,S as h,dB as f,cV as m,Q as g}from"./index-BNv9-hhu.js";import{C as P}from"./calendar-CWGSV0sS.js";import{S as w}from"./settings-DYTSD9ea.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],u=N("dollar-sign",S),k=(s={})=>j(a.property.getHousingList.queryOptions({staleTime:3e5,...s})),C=()=>{const s=p();return y(a.property.purchaseProperty.mutationOptions({onSuccess:()=>{d.success("Property purchased successfully!"),s.invalidateQueries({queryKey:a.property.getHousingList.key()}),s.invalidateQueries({queryKey:a.property.getUserProperties.key()}),s.invalidateQueries({queryKey:a.user.getCurrentUserInfo.key()})},onError:t=>{d.error(t?.message||"Failed to purchase property")}}))},q=()=>{const s=p();return y(a.property.sellProperty.mutationOptions({onSuccess:t=>{d.success(`Property sold for ${b(t.soldFor)}!`),s.invalidateQueries({queryKey:a.property.getHousingList.key()}),s.invalidateQueries({queryKey:a.property.getUserProperties.key()}),s.invalidateQueries({queryKey:a.user.getCurrentUserInfo.key()})},onError:t=>{d.error(t?.message||"Failed to sell property")}}))},L=()=>{const s=p();return y(a.property.setPrimaryProperty.mutationOptions({onSuccess:()=>{d.success("Primary property updated!"),s.invalidateQueries({queryKey:a.property.getHousingList.key()}),s.invalidateQueries({queryKey:a.property.getUserProperties.key()}),s.invalidateQueries({queryKey:a.user.getCurrentUserInfo.key()})},onError:t=>{d.error(t?.message||"Failed to set primary property")}}))},Q=({property:s,userCash:t,onPurchase:o,isPurchasing:l})=>{const i=t>=s.cost;return e.jsx("div",{className:"bg-slate-800/50 border border-slate-700/50 rounded-xl p-6 backdrop-blur-sm",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-500/20",children:e.jsx(f,{className:"h-6 w-6 text-indigo-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-1",children:s.name}),e.jsx("p",{className:"text-sm text-slate-300 mb-3",children:s.description}),e.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-1 text-green-400",children:[e.jsx(u,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:s.cost.toLocaleString()})]}),e.jsxs("div",{className:"text-slate-400 text-sm",children:[s.slots," slots"]})]}),e.jsx(m,{disabled:!i||l,className:`w-full ${i?"bg-indigo-600 hover:bg-indigo-700":"bg-slate-600 cursor-not-allowed"}`,onClick:()=>o(s.id),children:l?e.jsx(h,{}):i?"Purchase":"Insufficient Funds"})]})]})})},U=()=>{const{data:s,isLoading:t,error:o}=k(),{data:l}=v(),i=C(),r=c=>{i.mutate({propertyId:c})};return t?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(h,{})}):o?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-red-400",children:"Failed to load properties"})}):!s||s.length===0?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-slate-400",children:"No properties available"})}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Available Properties"}),e.jsx("div",{className:"grid gap-4",children:s.map(c=>e.jsx(Q,{property:c,userCash:l?.cash||0,isPurchasing:i.isPending,onPurchase:r},c.id))})]})},F=(s={})=>j(a.property.getUserProperties.queryOptions({staleTime:6e4,...s})),H=({userProperty:s,onSell:t,onSetPrimary:o,isSellingProperty:l,isSettingPrimary:i})=>{const{property:r}=s,c=Math.floor(r.cost*.2);return e.jsx("div",{className:`bg-slate-800/50 border rounded-xl p-6 backdrop-blur-sm ${s.isPrimary?"border-yellow-500/50 bg-yellow-500/5":"border-slate-700/50"}`,children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:`flex h-12 w-12 items-center justify-center rounded-lg ${s.isPrimary?"bg-yellow-500/20":"bg-indigo-500/20"}`,children:s.isPrimary?e.jsx(g,{className:"h-6 w-6 text-yellow-400"}):e.jsx(f,{className:"h-6 w-6 text-indigo-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:r.name}),s.isPrimary&&e.jsx("span",{className:"px-2 py-1 text-xs font-medium bg-yellow-500/20 text-yellow-400 rounded-full",children:"Primary"})]}),e.jsx("p",{className:"text-sm text-slate-300 mb-3",children:r.description}),e.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-1 text-green-400",children:[e.jsx(u,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:r.cost.toLocaleString()})]}),e.jsxs("div",{className:"text-slate-400 text-sm",children:[r.slots," slots"]}),e.jsxs("div",{className:"flex items-center gap-1 text-slate-400 text-sm",children:[e.jsx(P,{className:"h-4 w-4"}),e.jsxs("span",{children:["Purchased ",new Date(s.purchaseDate).toLocaleDateString()]})]})]}),r.buffs&&Object.keys(r.buffs).length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Buffs:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:Object.entries(r.buffs).map(([n,x])=>e.jsxs("span",{className:"px-2 py-1 text-xs bg-blue-500/20 text-blue-400 rounded-full",children:[n,":"," ",typeof x=="number"?`${((x-1)*100).toFixed(0)}%`:String(x)]},n))})]}),e.jsxs("div",{className:"flex gap-2",children:[!s.isPrimary&&e.jsxs(m,{variant:"outline",size:"sm",disabled:i,className:"flex items-center gap-1",onClick:()=>o(r.id),children:[e.jsx(g,{className:"h-4 w-4"}),"Set Primary"]}),!s.isPrimary&&e.jsxs(m,{variant:"destructive",size:"sm",disabled:l,className:"flex items-center gap-1",onClick:()=>t(r.id),children:[e.jsx(u,{className:"h-4 w-4"}),"Sell for $",c.toLocaleString()]}),e.jsxs(m,{disabled:!0,variant:"outline",size:"sm",className:"flex items-center gap-1",children:[e.jsx(w,{className:"h-4 w-4"}),"Customize"]})]})]})]})})},K=()=>{const{data:s,isLoading:t,error:o}=F(),l=q(),i=L(),r=n=>{l.mutate({propertyId:n})},c=n=>{i.mutate({propertyId:n})};return t?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(h,{})}):o?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-red-400",children:"Failed to load your properties"})}):!s||s.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("p",{className:"text-slate-400",children:"You don't own any properties yet"}),e.jsx("p",{className:"text-slate-500 text-sm mt-1",children:"Purchase a property to get started!"})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Your Properties"}),e.jsx("div",{className:"grid gap-4",children:s.map(n=>e.jsx(H,{userProperty:n,isSellingProperty:l.isPending,isSettingPrimary:i.isPending,onSell:r,onSetPrimary:c},n.id))})]})};function z(){return e.jsxs("div",{className:"space-y-6 w-full md:mx-auto md:max-w-4xl",children:[e.jsx("div",{className:"bg-slate-900/50 border border-slate-700/50 rounded-xl p-6 backdrop-blur-sm",children:e.jsx(K,{})}),e.jsx("div",{className:"bg-slate-900/50 border border-slate-700/50 rounded-xl p-6 backdrop-blur-sm",children:e.jsx(U,{})})]})}export{z as default};
