import { useEffect, useLayoutEffect, useRef, useState } from "react";
import { Roulette, type weaponAttributes } from "./RouletteHelper";
import "./roulette.css";
import Spinner from "@/components/Spinners/Spinner";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { useDailyChest } from "./useDailyChest";

interface LootboxRouletteProps {
    items: weaponAttributes[];
    itemsCount: number;
    transitionDuration: number;
    isSpin: boolean;
    setIsSpin: (isSpin: boolean) => void;
    handleClose: () => void;
}

const LootboxRoulette = ({
    items,
    itemsCount,
    transitionDuration,
    isSpin,
    setIsSpin,
    handleClose,
}: LootboxRouletteProps) => {
    const [rouletteWeapons, setRouletteWeapons] = useState<weaponAttributes[]>(items);
    const [weaponPrizeId, setWeaponPrizeId] = useState<number>(-1);
    const [isSpinEnd, setIsSpinEnd] = useState<boolean>(false);
    const [winHistory, setWinHistory] = useState<weaponAttributes[]>([]);
    const [spinComplete, setSpinComplete] = useState<boolean>(false);
    const { mutate: openChest, data: winnerData, isSuccess } = useDailyChest();
    const isMobile = useCheckMobileScreen() as boolean;

    const rouletteContainerRef = useRef<HTMLDivElement>(null);
    const weaponsRef = useRef<HTMLDivElement>(null);
    const rouletteRef = useRef<Roulette | null>(null);

    const transitionEndHandler = () => {
        setWinHistory(winHistory.concat(rouletteWeapons[weaponPrizeId]));
        setIsSpin(false);
        setIsSpinEnd(true);
    };

    useLayoutEffect(() => {
        if (!rouletteContainerRef.current || !weaponsRef) return;

        const rouletteInstance = new Roulette({
            winner: null,
            weapons: items,
            rouletteContainerRef: rouletteContainerRef.current,
            weaponsRef: weaponsRef,
            weaponsCount: itemsCount,
            transitionDuration,
            itemWidth: isMobile ? 100 : 125,
        });

        rouletteInstance.set_weapons();
        setRouletteWeapons(rouletteInstance.weapons);

        rouletteRef.current = rouletteInstance;
    }, [items, itemsCount, transitionDuration, isMobile]);

    const play = async () => {
        openChest();
    };

    useEffect(() => {
        if (isSuccess && winnerData) {
            const winner = items.find((w) => w.itemId === winnerData.itemId);
            if (winner && rouletteRef.current) {
                rouletteRef.current.setWinner(winner);

                setSpinComplete(true);
                setIsSpin(true);

                setTimeout(() => {
                    if (rouletteRef.current) {
                        const id = rouletteRef.current.spin();
                        setWeaponPrizeId(id);
                    }
                }, 1000);
            }
        }
    }, [isSuccess, winnerData, items]);

    return (
        <div>
            <div className="rouletteWrapper">
                <div ref={rouletteContainerRef}>
                    <div className="relative h-[160px] w-[700px] overflow-hidden rounded-[5px] border border-[#232730] md:w-[875px]">
                        <div className="evTarget"></div>
                        <div ref={weaponsRef} className="evWeapons" onTransitionEnd={transitionEndHandler}>
                            {rouletteWeapons?.map((w, i) => {
                                return (
                                    <RouletteItem
                                        key={i}
                                        id={i}
                                        isLoser={i !== weaponPrizeId && !isSpin && isSpinEnd}
                                        itemName={w.itemName}
                                        rarity={w.rarity}
                                        itemImage={w.itemImage}
                                        itemQuantity={w.itemQuantity}
                                    />
                                );
                            })}
                        </div>
                    </div>
                </div>

                {spinComplete ? (
                    <div className="mt-5 flex h-16 flex-col gap-2">
                        {winHistory?.length > 0 && (
                            <div className="flex flex-col gap-2">
                                <p className="font-body font-semibold text-lg text-white md:text-2xl">
                                    You received <span className="text-custom-yellow">{winHistory[0]?.itemName} </span>{" "}
                                    x{winHistory[0]?.itemQuantity}!
                                </p>
                                <button
                                    className="mx-auto w-1/5 cursor-pointer rounded-md border border-gray-700 bg-red-600 p-2 px-5 text-stroke-sm text-white hover:brightness-95"
                                    onClick={() => handleClose()}
                                >
                                    Close
                                </button>
                            </div>
                        )}
                    </div>
                ) : (
                    <button
                        type="button"
                        disabled={isSpin || spinComplete}
                        className="weapons-center darkBlueButtonBGSVG mx-auto mt-5 flex h-16 w-1/2 justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs disabled:opacity-75 disabled:brightness-75 md:w-3/4 dark:text-slate-200"
                        onClick={play}
                    >
                        <img
                            className="-ml-4 my-auto mr-2.5 inline-block size-8"
                            src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/KWEkQyF.png`}
                            alt=""
                        />
                        <span className="my-auto text-2xl">Open</span>
                    </button>
                )}
            </div>
        </div>
    );
};

interface LootboxWrapperProps {
    potentialChestItems: weaponAttributes[];
    isLoading: boolean;
    isSpin: boolean;
    setIsSpin: (isSpin: boolean) => void;
    handleClose: () => void;
}

const LootboxWrapper = ({ potentialChestItems, isLoading, isSpin, setIsSpin, handleClose }: LootboxWrapperProps) => {
    const weaponsCount = 150;
    const transitionDuration = 5;
    return (
        <div className={"LootboxWrapper"}>
            {isLoading ? (
                <Spinner center />
            ) : (
                <LootboxRoulette
                    items={potentialChestItems}
                    itemsCount={weaponsCount}
                    transitionDuration={transitionDuration}
                    isSpin={isSpin}
                    setIsSpin={setIsSpin}
                    handleClose={handleClose}
                />
            )}
        </div>
    );
};

export default LootboxWrapper;

interface RouletteItemProps {
    id: number;
    itemName: string;
    rarity: string;
    itemImage: string;
    isLoser: boolean;
    itemQuantity: string;
}

const RouletteItem = ({ id, itemName, rarity, itemImage, isLoser, itemQuantity }: RouletteItemProps) => {
    return (
        <div className="evWeapon" style={isLoser ? { opacity: "0.5" } : { opacity: "1" }}>
            <div className={`evWeaponInner`} id={String(id)}>
                <div className={`evWeaponBorder`}></div>

                <img
                    className="w-[70px]! h-[70px]! absolute! top-[25px]! left-[50%]! -translate-x-1/2"
                    src={itemImage}
                    alt={itemName}
                />

                <span className="-translate-x-1/2 absolute top-0 left-[25px] font-body font-semibold text-base text-blue-500 text-stroke-s-sm">
                    {itemQuantity}x
                </span>

                <div className={`evWeaponRarity ` + rarity}></div>
                <div className="evWeaponText mt-auto font-body font-semibold text-stroke-sm">
                    <p className={`text-sm! ` + rarity}>{itemName}</p>
                </div>
            </div>
        </div>
    );
};
