import NotificationBadge from "@/components/NotificationBadge";
import { getModifierText, getStatusEffectDetails, statusEffects, ProcessedStatusEffect } from "@/helpers/statusEffects";
import { getReturnValues } from "@/hooks/useCountdown";
import { cn } from "@/lib/utils";
import { type StatusEffect } from "@/hooks/api/useGetStatusEffects";

interface StatusEffectsProps {
    type?: "player" | string;
    currentEffects: Record<string, any> | StatusEffect[] | null | undefined;
    isBattle?: boolean;
    className?: string;
}

const StatusEffects = ({ type = "player", currentEffects, isBattle = false, className }: StatusEffectsProps) => {
    if (!currentEffects) return null;

    const activeEffects: ProcessedStatusEffect[] = [];

    // Handle array of StatusEffect objects (from API)
    if (Array.isArray(currentEffects)) {
        for (const statusEffectData of currentEffects) {
            if (!statusEffectData || typeof statusEffectData !== "object") continue;

            const effect = statusEffectData.effect;
            if (!effect) continue;

            const processedEffect: ProcessedStatusEffect = {
                id: statusEffectData.id,
                name: statusEffectData.customName || effect.name,
                description: effect.description,
                tier: effect.tier,
                source: effect.source,
                type: effect.effectType?.toLowerCase(),
                count: statusEffectData.stacks,
                endsAt: statusEffectData.endsAt,
                customName: statusEffectData.customName,
                modifier: effect.modifier,
                // Add icon from statusEffects mapping
                ...statusEffects[effect.source as keyof typeof statusEffects],
            };

            activeEffects.push(processedEffect);
        }
    } else {
        // Handle Record<string, any> format (for battle effects)
        for (const [key, value] of Object.entries(currentEffects)) {
            let statusEffect: ProcessedStatusEffect | null = null;

            if (isBattle) {
                const battleEffect = getStatusEffectDetails(key, value);
                if (battleEffect) {
                    statusEffect = {
                        ...battleEffect,
                        turns: value.turns,
                        value: value,
                    };
                }
            } else {
                const effect = value.effect;
                if (effect) {
                    statusEffect = {
                        ...effect,
                        count: value.stacks,
                        endsAt: value?.endsAt,
                        customName: value.customName,
                        ...statusEffects[effect?.source as keyof typeof statusEffects],
                    };
                }
            }

            if (statusEffect) {
                activeEffects.push(statusEffect);
            }
        }
    }

    const formatTime = (timestamp: string | bigint | undefined): string | null => {
        if (timestamp) {
            // Handle both string and bigint timestamps
            const timestampNum = typeof timestamp === "bigint" ? Number(timestamp) : new Date(timestamp).getTime();
            const formatted = timestampNum - new Date().getTime();
            const values = getReturnValues(formatted);
            return `${Number.parseInt(values[1] as string)}h  ${values[2]}m`;
        }
        return null;
    };

    return (
        <div
            className={cn(
                "mx-3 mb-1 flex flex-row md:mb-0 2xl:gap-3.5",
                type !== "player" ? "flex-row-reverse" : "flex-row",
                isBattle ? "gap-4" : "gap-3",
                className
            )}
        >
            {activeEffects.map((effect, i) => (
                <div
                    key={effect.id ? effect.id : i}
                    data-tooltip-id="statuseffect-tooltip"
                    data-effect-name={effect.customName ? effect.customName : effect.name}
                    data-effect-turns={effect.hideTurns ? null : effect?.turns}
                    data-effect-description={effect.description ? effect.description : null}
                    data-effect-tier={effect.tier ? effect.tier : null}
                    data-effect-source={effect?.source ? effect?.source : null}
                    data-effect-amount={effect.count ? effect.count : null}
                    data-effect-endsat={effect.endsAt ? formatTime(effect.endsAt) : null}
                    className="relative"
                    data-effect-modifier={
                        effect.modifier && effect.modifier !== 0 && effect.source
                            ? getModifierText(effect.modifier, effect.source)
                            : null
                    }
                >
                    {effect.count && effect.count > 0 && (
                        <div className="-bottom-1 -right-2 absolute flex size-4 items-center justify-center rounded-full border border-black bg-red-500 font-body font-semibold text-[0.7rem] text-stroke-sm text-white">
                            {effect.count}
                        </div>
                    )}

                    <img
                        src={effect.icon}
                        alt=""
                        className={cn(
                            effect?.type === "buff" ? "ring-sky-500" : "ring-red-500",
                            "mt-1 h-8 w-auto rounded-md ring-2 md:h-7 2xl:h-8"
                        )}
                    />
                    {isBattle && !effect.hideTurns && effect.turns && effect.turns > 0 ? (
                        <NotificationBadge amount={effect.turns} className="-bottom-2 -end-3 size-5!" />
                    ) : null}
                </div>
            ))}
        </div>
    );
};

export default StatusEffects;
