import Button from "@/components/Buttons/Button";
import { DisplayItem } from "@/components/DisplayItem";
import DataTable from "@/components/Tables/DataTable";
import useActivateItem from "@/hooks/api/useActivateItem";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import { rarityColours } from "@/helpers/rarityColours";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useIsTalentUnlocked from "@/hooks/api/useIsTalentUnlocked";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { cn } from "@/lib/utils";
import { useMutation } from "@tanstack/react-query";
import React, { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import { itemStatDisplay } from "../../../components/Inventory/itemStatDisplay";
import LootboxModal from "../../../components/Lootbox/LootboxModal";
import useEquipItem from "../api/useEquipItem";
import useMaterialsCrate from "../api/useMaterialsCrate";
import useToolsCrate from "../api/useToolsCrate";
import SpecialItemModal from "./SpecialItemModal";
import useGetInventory from "@/hooks/api/useGetInventory";
import type { InventoryItem, Item } from "@/types/item";

interface EquippedItems {
    [key: string]: {
        userItemId: number;
        id: number;
    };
}

interface InventoryTableProps {
    equippedItems: EquippedItems;
}

function InventoryTable({ equippedItems }: InventoryTableProps) {
    const isMobile = useCheckMobileScreen();
    const { isLoading, error, data } = useGetInventory();
    const [selectedTab, setSelectedTab] = useState<string>("All");
    const [specialItemModalOpen, setSpecialItemModalOpen] = useState<boolean>(false);
    const [specialItem, setSpecialItem] = useState<Item>({} as Item);
    const metabolismTalent = useIsTalentUnlocked("good_stomach");
    const [lootboxModalOpen, setLootboxModalOpen] = useState<boolean>(false);
    const { equipItem } = useEquipItem();
    const { data: currentUser } = useFetchCurrentUser();
    const useItemMutation = useActivateItem();
    const [rowData, setRowData] = useState<InventoryItem[] | null>(null);
    const gridRef = useRef<any>(null);

    const SPECIAL_USE_ITEMS = [
        "Death Book",
        "Megaphone",
        "Life Book",
        "Kompromat",
        "Daily Chest",
        "Small Raw Materials Crate",
        "Small Tools Crate",
    ];

    const postUseNormalItem = async (userItem: InventoryItem) => {
        const { item } = userItem;
        if (item.health && item.health > 0) {
            if (currentUser?.currentHealth === currentUser?.health) {
                toast.error("You're already full hp!");
                return;
            }
        }
        if (
            (currentUser?.jailedUntil && currentUser.jailedUntil > 0) ||
            (currentUser?.hospitalisedUntil && currentUser.hospitalisedUntil > 0)
        ) {
            toast.error("You can't use this in your current state!");
            return;
        }

        try {
            const response = await useItemMutation.mutateAsync({
                userItemId: userItem.id,
            });

            // Custom success messages for different item types
            if (item.health && item.health > 0) {
                toast.success(`You recovered ${item.health} HP!`);
            }
            if (item.energy && item.energy > 0) {
                toast.success(`You recovered ${item.energy} Energy!`);
            }
            if (item.actionPoints && item.actionPoints > 0) {
                toast.success(`You recovered ${item.actionPoints} AP!`);
            }

            return response;
        } catch (err) {
            console.error("Error using item:", err);
            throw err;
        }
    };

    const useNormalItem = useMutation({
        mutationFn: postUseNormalItem,
        onError: (err: any) => {
            console.log(err);
        },
        onSuccess: (res: any) => {
            // Additional success handling for specific item effects
            if (res?.info?.injuryRemoved) {
                toast.success(`Injury treated successfully!`);
                return;
            }
            if (res?.info?.recipeUnlocked) {
                toast.success(`Recipe unlocked successfully!`);
                return;
            }
            // Generic success message only if no specific item effects
            if (!res?.info?.injuryRemoved && !res?.info?.recipeUnlocked) {
                toast.success(`Item used successfully!`);
            }
        },
    });

    const { mutate: redeemMaterialsCrate } = useMaterialsCrate();
    const { mutate: redeemToolsCrate } = useToolsCrate();

    const handleUseItem = (userItem: InventoryItem) => {
        const { item } = userItem;
        if (currentUser?.hospitalisedUntil && currentUser.hospitalisedUntil > 0) {
            toast.error("Cant use items while hospitalised!");
            return;
        }
        if (currentUser?.jailedUntil && currentUser.jailedUntil > 0) {
            toast.error("Cant use items while jailed!");
            return;
        }
        if (currentUser?.missionEnds && currentUser.missionEnds > 0) {
            toast.error("Cant use items while on a mission!");
            return;
        }
        if (item?.itemType === "special") {
            if (item?.name === "Daily Chest") {
                setLootboxModalOpen(true);
                return;
            }
            if (item?.name === "Small Raw Materials Crate") {
                redeemMaterialsCrate();
                return;
            }
            if (item?.name === "Small Tools Crate") {
                redeemToolsCrate();
                return;
            }

            setSpecialItemModalOpen(true);
            setSpecialItem(item);
        } else {
            useNormalItem.mutate(userItem);
        }
    };

    const handleEquip = (userItem: InventoryItem) => {
        equipItem.mutate({ currentUser, userItem });
    };

    interface DisplayActionsProps {
        equippedItems: EquippedItems;
        data: InventoryItem;
    }

    const DisplayActions = (props: DisplayActionsProps) => {
        // eslint-disable-next-line no-shadow
        const { equippedItems } = props;
        const userItem = props.data;

        if (!userItem) return null;

        const { item } = userItem;

        const isSpecialItemType = (type: string) =>
            ["consumable", "crafting", "upgrade", "quest", "junk", "recipe", "pet"].includes(type);
        const isSpecialItemName = (name: string) => SPECIAL_USE_ITEMS.includes(name);
        const isItemTypeWithUseButton = (type: string) => ["consumable", "special", "recipe", "pet"].includes(type);

        const isEquipped = equippedItems?.[item.itemType]?.userItemId === userItem.id;
        const canEquip = currentUser?.level && item.level ? currentUser.level >= item.level : false;

        const renderUseButton = () => (
            <Button className="text-base" onClick={() => handleUseItem(userItem)}>
                Use
            </Button>
        );

        const renderEquipButton = () => (
            <Button disabled={!canEquip} className="text-base" onClick={() => handleEquip(userItem)}>
                Equip
            </Button>
        );

        return (
            <div className="flex size-full items-center justify-center">
                {isSpecialItemType(item.itemType) || isSpecialItemName(item.name) ? (
                    <>{isItemTypeWithUseButton(item.itemType) ? renderUseButton() : null}</>
                ) : (
                    <>
                        {isEquipped ? (
                            <p className="m-auto inline font-semibold text-base text-custom-yellow md:text-sm">
                                Equipped
                            </p>
                        ) : (
                            renderEquipButton()
                        )}
                    </>
                )}
            </div>
        );
    };

    useEffect(() => {
        if (gridRef.current && gridRef.current.api) {
            if (selectedTab === "All") {
                gridRef.current.api.setFilterModel({
                    itemTypeFilter: {
                        type: "notEqual",
                        filter: null,
                    },
                });
            } else {
                gridRef.current.api.setFilterModel({
                    itemTypeFilter: {
                        type: "includes",
                        filter: itemTypeFilters[selectedTab],
                    },
                });
            }
        }
    }, [selectedTab]);

    interface DisplayItemCellProps {
        value: Item;
        data: InventoryItem;
    }

    const DisplayItemCell = React.memo((props: DisplayItemCellProps) => {
        const { value: item } = props;
        const upgradeLevel = props.data.upgradeLevel || 0;

        return (
            <div className="relative flex h-full items-center gap-3 px-1 py-2 md:w-full md:flex-row md:items-start md:gap-4 md:p-1">
                <DisplayItem itemTypeFrame item={props.data} className="my-auto size-14" />
                <div className="flex flex-1 flex-col gap-1.5 py-1.5 md:my-auto md:gap-0">
                    <p
                        className={cn(
                            item.name && item.name.length > 15
                                ? "md:text-sm! text-[0.65rem]"
                                : "text-sm! md:text-base!",
                            "text-wrap! leading-none! truncate text-left font-semibold text-custom-yellow md:text-base"
                        )}
                    >
                        {item.name}
                        {upgradeLevel > 0 && <span> +{upgradeLevel}</span>}
                    </p>
                    <p
                        className={cn(
                            rarityColours(item.rarity),
                            "text-xs! leading-none! md:text-sm! text-left font-semibold"
                        )}
                    >
                        {displayItemType(item.itemType)}
                    </p>
                    <div className="mt-1 flex flex-col font-bold text-slate-700 text-xs dark:text-indigo-400">
                        {itemStatDisplay(item, metabolismTalent).map((stat: string) => (
                            <p key={stat}>{stat}</p>
                        ))}
                    </div>
                </div>
            </div>
        );
    });
    DisplayItemCell.displayName = "DisplayItemCell";

    const itemTypeFilters: Record<string, string[]> = {
        All: [],
        Weapons: ["weapon", "ranged", "offhand"],
        Armor: ["head", "chest", "hands", "legs", "feet", "finger", "shield"],
        Consumables: ["consumable", "recipe", "pet"],
        Material: ["crafting", "upgrade"],
        Misc: ["special", "quest", "junk"],
    };

    const displayItemType = (itemType: string): string => {
        if (itemType === "weapon") return "Melee Weapon";
        if (itemType === "ranged") return "Ranged Weapon";
        if (itemType === "quest") return "Task Item";
        if (itemType === "crafting") return "Material";
        if (itemType === "special") return "Special Item";
        return capitaliseFirstLetter(itemType);
    };

    useEffect(() => {
        if (data) {
            setRowData(data);
        }
    }, [data]);

    const cols = [
        {
            headerName: "Item",
            field: "item",
            cellRenderer: DisplayItemCell,
            cellClass: "items-center! flex!",
            minWidth: isMobile ? 183 : 250,
            sortable: false,
            autoHeight: true,
            wrapText: true,
            getQuickFilterText: (params) => {
                return params.data.item.itemType;
            },
            filter: "agTextColumnFilter",
            valueFormatter: (params) => {
                return params.data.item;
            },
            filterValueGetter: (params) => {
                return params.data.item.name;
            },
            filterParams: {
                filterOptions: ["contains"],
                defaultOption: "contains",
            },
        },
        {
            headerName: isMobile ? "Qty" : "Quantity",
            field: "count",
            headerClass: "centerGridHeader",
            maxWidth: 120,
            cellClass: "items-center! justify-center! flex! font-semibold font-body text-base",
            filter: "agNumberColumnFilter",
            filterParams: {
                filterOptions: ["equals", "greaterThan", "lessThan", "inRange"],
                defaultOption: "equals",
            },
        },
        {
            headerName: "Category",
            field: "item.itemType",
            headerClass: "centerGridHeader",
            hide: isMobile,
            cellClass: "md:text-base text-sm font-semibold text-center flex! items-center! justify-center! truncate",
            valueFormatter: (params) => {
                return displayItemType(params.data.item.itemType);
            },
        },
        {
            headerName: "Level",
            field: "item.level",
            hide: isMobile,
            initialSortIndex: 1,
            maxWidth: 120,
            sort: "desc",
            cellClass: "items-center! justify-center! flex! font-semibold font-body text-base",
            headerClass: "centerGridHeader",
            filterParams: {
                filterOptions: ["equals", "greaterThan", "lessThan", "inRange"],
                defaultOption: "equals",
            },
        },
        {
            headerName: "Actions",
            field: "actions",
            sortable: false,
            headerClass: "centerGridHeader",
            cellClass: "flex! items-center! justify-center!",
            cellRenderer: DisplayActions,
            cellRendererParams: {
                equippedItems: equippedItems,
            },
            valueFormatter: (params) => {
                return params.data;
            },
            filter: false,
            floatingFilter: false,
        },
        {
            headerName: "CategoryHidden",
            field: "itemTypeFilter",
            hide: true,
            filterParams: {
                filterOptions: [
                    {
                        displayKey: "includes",
                        displayName: "Includes",
                        predicate: ([filterValue], cellValue) => filterValue.includes(cellValue),
                    },
                ],
            },
            filterValueGetter: (params) => {
                return params.data.item?.itemType;
            },
        },
    ];

    const [colDefs, setColDefs] = useState<any[]>(cols);

    useEffect(() => {
        const params = {
            force: true,
            suppressFlash: true,
            columns: ["actions"],
        };
        gridRef?.current?.api?.refreshCells(params);
        setColDefs((prevColDefs) => {
            return prevColDefs.map((col) => {
                if (col.field === "actions") {
                    return {
                        ...col,
                        cellRendererParams: {
                            equippedItems: equippedItems,
                        },
                    };
                }
                return col;
            });
        });
    }, [equippedItems]);

    if (error) return "An error has occurred: " + error.message;

    return (
        <>
            <InventoryTabs selectedTab={selectedTab} setSelectedTab={setSelectedTab} />
            <LootboxModal openModal={lootboxModalOpen} setOpenModal={setLootboxModalOpen} />
            <SpecialItemModal
                open={specialItemModalOpen}
                setOpen={setSpecialItemModalOpen}
                title={specialItem.name}
                item={specialItem}
                currentUser={currentUser}
            />
            <div className="mb-8 md:mb-0 md:max-w-6xl 2xl:mx-auto">
                <DataTable
                    keyProp={JSON.stringify(equippedItems)}
                    dataList={rowData}
                    colDefs={colDefs}
                    isLoading={isLoading}
                    customGridRef={gridRef}
                />
            </div>
        </>
    );
}

interface InventoryTabsProps {
    selectedTab: string;
    setSelectedTab: (tab: string) => void;
}

const InventoryTabs = ({ selectedTab, setSelectedTab }: InventoryTabsProps) => {
    const currentTab = (tabname: string): boolean => {
        if (selectedTab === tabname) {
            return true;
        }
        return false;
    };
    const tabs = [
        { name: "All", current: currentTab("All") },
        { name: "Weapons", current: currentTab("Weapons") },
        { name: "Armor", current: currentTab("Armor") },
        { name: "Consumables", current: currentTab("Consumables") },
        { name: "Material", current: currentTab("Material") },
        { name: "Misc", current: currentTab("Misc") },
    ];
    return (
        <div>
            <div className="mx-2 my-1 2xl:hidden">
                <label htmlFor="tabs" className="sr-only">
                    Select a tab
                </label>
                {/* Use an "onChange" listener to redirect the user to the selected tab URL. */}
                <select
                    id="tabs"
                    name="tabs"
                    className="mb-3 block w-full rounded-md border-gray-300 px-2 text-stroke-md focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white"
                    defaultValue={tabs.find((tab) => tab.current).name}
                    onChange={(e) => setSelectedTab(e.target.value)}
                >
                    {tabs.map((tab) => (
                        <option key={tab.name}>{tab.name}</option>
                    ))}
                </select>
            </div>
            <div className="hidden 2xl:block">
                <nav
                    className="relative z-0 flex divide-x divide-gray-200 border-gray-600 border-b shadow-sm dark:divide-gray-600"
                    aria-label="Tabs"
                >
                    {tabs.map((tab, tabIdx) => (
                        <button
                            key={tab.name}
                            aria-current={tab.current ? "page" : undefined}
                            className={cn(
                                tab.current ? "text-gray-900" : "text-gray-500 hover:text-gray-700",
                                tabIdx === 0 ? "rounded-tl-lg" : "",
                                tabIdx === tabs.length - 1 ? "rounded-tr-lg" : "",
                                "group relative min-w-0 flex-1 overflow-hidden bg-white p-4 text-center font-medium text-sm hover:bg-gray-50 focus:z-10 dark:bg-gray-800 dark:text-white"
                            )}
                            onClick={() => setSelectedTab(tab.name)}
                        >
                            <span>{tab.name}</span>
                            <span
                                aria-hidden="true"
                                className={cn(
                                    tab.current ? "bg-indigo-500" : "bg-transparent",
                                    "absolute inset-x-0 bottom-0 h-0.5"
                                )}
                            />
                        </button>
                    ))}
                </nav>
            </div>
        </div>
    );
};

export default InventoryTable;
