import{j as e,h as a,o as u,t as h,bN as p,e as y,C as k,g as j,c as d,ap as c,ar as C,K as v}from"./index-BNv9-hhu.js";import w from"./TalentTree-BUBhUAaT.js";import{a as N,d as T,i as E,s as B}from"./stamina-LF5EZP0M.js";import"./talentData-CZzZ7076.js";import"./useGetUserSkills-D1K9h3Ou.js";const I=({className:r,href:n,text:s,onClick:l,disabled:t=!1,textHighlight:o})=>e.jsx("button",{disabled:t,className:a("mt-2 flex select-none flex-col",r),onClick:l&&(()=>l()),children:n?e.jsx(u,{to:n,className:a("roundedBtn block w-full rounded-xl bg-[#f6e58d] px-5 py-2 text-center",t?"brightness-50":"hover:brightness-105"),children:e.jsx("span",{children:s})}):e.jsx("div",{className:a("roundedBtn block w-full cursor-pointer rounded-xl bg-[#f6e58d] px-5 py-2 text-center ",t?"brightness-50":"hover:brightness-105"),children:e.jsxs("span",{children:[s,o&&e.jsxs("span",{className:"text-indigo-800 dark:text-indigo-600 font-mono font-semibold",children:[" ",o]})]})})});function L(){const{data:r}=h(),{data:n}=p(),s=y(),{TALENTS_LEVEL_GATE:l,TALENT_RESPEC_BASE_COST:t}=k(),o=r?.level<l||n?.talentList?.length===0,m=r?.level*t||0,f=j(d.talents.resetTalents.mutationOptions({onSuccess:()=>{s.invalidateQueries({queryKey:d.talents.getUnlockedTalents.key()}),s.invalidateQueries({queryKey:d.user.getCurrentUserInfo.key()}),c.success("Your talents were reset!")},onError:x=>{const b=x.message||"Failed to reset talents";console.error(b),c.error(b)}})),g=()=>{if(r?.cash<m){c.error("You don't have enough cash to respec!");return}window.confirm("This will reset all talents and abilities, are you sure?")&&f.mutate()};return e.jsx(I,{disabled:o,className:"mx-auto mr-5 mb-4 w-auto text-sm md:mr-20 md:mb-8 dark:text-black font-display",text:"Respec Talents",textHighlight:`(¥${m})`,onClick:()=>g()})}function V(){const r=C();return r.pathname==="/talents"?e.jsxs("div",{className:"md:mx-auto md:max-w-6xl",children:[e.jsx(L,{}),e.jsxs("div",{className:"m-5 flex w-full flex-col gap-3 md:gap-4",children:[e.jsx(i,{text:"Strength",href:"strength",bgCol:" from-blue-600 to-blue-800 before:from-blue-600 before:to-blue-800",borderCol:"before:border-blue-500 border-blue-500",image:v}),e.jsx(i,{text:"Dexterity",href:"dexterity",image:N,bgCol:" from-amber-600 to-amber-800 before:from-amber-600 before:to-amber-800",borderCol:"before:border-amber-500 border-amber-500"}),e.jsx(i,{text:"Defence",href:"defence",image:T,bgCol:"from-green-600 to-green-800 before:from-green-600 before:to-green-800",borderCol:"before:border-green-500 border-green-500"}),e.jsx(i,{text:"Intelligence",href:"intelligence",image:E,bgCol:"from-indigo-600 to-indigo-800 before:from-indigo-600 before:to-indigo-800",borderCol:"before:border-indigo-500 border-indigo-500"}),e.jsx(i,{text:"Endurance",href:"endurance",image:B,bgCol:"from-sky-600 to-sky-800 before:from-sky-600 before:to-sky-800",borderCol:"border-sky-500 before:border-sky-500"}),e.jsx(i,{disabled:!0,text:"Vitality",href:"vitality",image:"https://cloudflare-image.jamessut.workers.dev/static/talents/icons/charisma.png",bgCol:"bg-gray-800 before:bg-gray-800",borderCol:"border-black before:border-black"})]})]}):e.jsx(w,{pathname:r.pathname})}const i=({text:r,href:n,bgCol:s,image:l,disabled:t,borderCol:o})=>e.jsxs("div",{className:"relative",children:[e.jsx(u,{to:n,children:e.jsxs("div",{className:a("font-accent skewedContainer -ml-3 flex 3xl:h-24 h-18 w-[90%] border-y-2 border-l-2 bg-linear-to-t shadow-2xl before:border-y-2 before:border-r-2 before:bg-linear-to-t active:brightness-125 xl:h-[4.1rem]",s,o),children:[e.jsx("img",{className:a(t&&"grayscale","z-10 my-auto ml-2 h-[90%] drop-shadow-xl"),src:l,alt:""}),e.jsx("p",{className:a("z-10 m-auto text-2xl text-stroke-s-sm uppercase",t?"text-gray-600":"dark:text-slate-200"),children:r})]})}),e.jsx("div",{className:a("wavyBackgroundAfter right-[-4px] z-10 my-0.5 w-[8%] cursor-default border-l-2 bg-[#111521] md:w-[10%]",o)}),e.jsx("div",{className:"wavyBackground -ml-2.5 pointer-events-none absolute top-0 left-0 mt-0.5 h-[94%] w-full invert"})]});export{V as default};
